<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">
<fontconfig>
  <!-- Generic name aliasing -->
  <alias>
    <family>serif</family>
    <prefer>
      <family>Standard Symbols PS</family>
    </prefer>
  </alias>

  <!-- Generic name assignment -->
  <alias>
    <family>Standard Symbols PS</family>
    <default>
      <family>serif</family>
    </default>
  </alias>

  <!-- Original PostScript base font mapping -->
  <alias binding="same">
    <family>Standard Symbols PS</family>
    <default>
      <family>Symbol</family>
    </default>
  </alias>

  <!-- Font substitution rules -->
  <alias binding="same">
    <family>Symbol</family>
    <accept>
      <family>Standard Symbols PS</family>
    </accept>
  </alias>

  <alias binding="same">
    <family>SymbolNeu</family>
    <accept>
      <family>Standard Symbols PS</family>
    </accept>
  </alias>

  <alias binding="same">
    <family>Symbol Neu for Powerline</family>
    <accept>
      <family>Standard Symbols PS</family>
    </accept>
  </alias>
</fontconfig>
