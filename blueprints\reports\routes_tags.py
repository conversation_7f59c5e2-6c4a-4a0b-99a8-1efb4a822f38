# -*- coding: utf-8 -*-
from flask import render_template, request, redirect, url_for, flash, jsonify
from . import reports_bp
from services.report_tag_service import ReportTagService
from services.report_service import ReportService
import logging

# Inicializar servicios
tag_service = ReportTagService()
report_service = ReportService()

@reports_bp.route('/etiquetas')
def manage_tags():
    """Gestionar etiquetas de informes"""
    try:
        tags = tag_service.get_all_tags()
        tag_stats = tag_service.get_tag_statistics()
        
        return render_template('reports/manage_tags.html',
                             tags=tags,
                             tag_stats=tag_stats)
    except Exception as e:
        logging.error(f"Error al cargar gestión de etiquetas: {str(e)}")
        flash('Error al cargar la gestión de etiquetas', 'error')
        return redirect(url_for('reports.index'))

@reports_bp.route('/etiquetas/crear', methods=['POST'])
def create_tag():
    """Crear una nueva etiqueta"""
    try:
        name = request.form.get('name')
        color = request.form.get('color', 'primary')
        
        if not name:
            flash('El nombre de la etiqueta es obligatorio', 'error')
            return redirect(url_for('reports.manage_tags'))
        
        tag = tag_service.create_tag(name, color)
        flash(f'Etiqueta "{tag.name}" creada correctamente', 'success')
        
    except Exception as e:
        logging.error(f"Error al crear etiqueta: {str(e)}")
        flash('Error al crear la etiqueta', 'error')
    
    return redirect(url_for('reports.manage_tags'))

@reports_bp.route('/etiquetas/<int:tag_id>/editar', methods=['POST'])
def edit_tag(tag_id):
    """Editar una etiqueta"""
    try:
        name = request.form.get('name')
        color = request.form.get('color')
        
        if not name:
            flash('El nombre de la etiqueta es obligatorio', 'error')
            return redirect(url_for('reports.manage_tags'))
        
        tag = tag_service.update_tag(tag_id, name=name, color=color)
        if tag:
            flash(f'Etiqueta "{tag.name}" actualizada correctamente', 'success')
        else:
            flash('Etiqueta no encontrada', 'error')
            
    except Exception as e:
        logging.error(f"Error al editar etiqueta {tag_id}: {str(e)}")
        flash('Error al editar la etiqueta', 'error')
    
    return redirect(url_for('reports.manage_tags'))

@reports_bp.route('/etiquetas/<int:tag_id>/eliminar', methods=['POST'])
def delete_tag(tag_id):
    """Eliminar una etiqueta"""
    try:
        if tag_service.delete_tag(tag_id):
            flash('Etiqueta eliminada correctamente', 'success')
        else:
            flash('Etiqueta no encontrada', 'error')
    except Exception as e:
        logging.error(f"Error al eliminar etiqueta {tag_id}: {str(e)}")
        flash('Error al eliminar la etiqueta', 'error')
    
    return redirect(url_for('reports.manage_tags'))

@reports_bp.route('/informes/<filename>/etiquetar', methods=['POST'])
def tag_report(filename):
    """Etiquetar un informe"""
    try:
        tag_id = request.form.get('tag_id', type=int)
        
        if not tag_id:
            return jsonify({'success': False, 'error': 'ID de etiqueta requerido'})
        
        association = tag_service.tag_report(filename, tag_id)
        if association:
            tag = tag_service.get_tag_by_id(tag_id)
            return jsonify({
                'success': True,
                'tag': tag.to_dict() if tag else None
            })
        else:
            return jsonify({'success': False, 'error': 'Error al etiquetar informe'})
            
    except Exception as e:
        logging.error(f"Error al etiquetar informe {filename}: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@reports_bp.route('/informes/<filename>/desetiquetar', methods=['POST'])
def untag_report(filename):
    """Eliminar etiqueta de un informe"""
    try:
        tag_id = request.form.get('tag_id', type=int)
        
        if not tag_id:
            return jsonify({'success': False, 'error': 'ID de etiqueta requerido'})
        
        if tag_service.untag_report(filename, tag_id):
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': 'Error al desetiquetar informe'})
            
    except Exception as e:
        logging.error(f"Error al desetiquetar informe {filename}: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@reports_bp.route('/informes/<filename>/etiquetas')
def get_report_tags(filename):
    """Obtener etiquetas de un informe"""
    try:
        tags = tag_service.get_report_tags(filename)
        return jsonify({
            'success': True,
            'tags': [tag.to_dict() for tag in tags]
        })
    except Exception as e:
        logging.error(f"Error al obtener etiquetas del informe {filename}: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})