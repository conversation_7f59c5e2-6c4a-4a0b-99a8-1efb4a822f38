('d:\\Proyectos Python\\Pruebas Empleados con importación '
 'Qoder\\build_temp\\empleados_app_v1.0.10\\empleados_app_v1.0.10.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'd:\\Proyectos Python\\Pruebas Empleados con importación '
   'Qoder\\build_temp\\empleados_app_v1.0.10\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'd:\\Proyectos Python\\Pruebas Empleados con importación '
   'Qoder\\build_temp\\empleados_app_v1.0.10\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'd:\\Proyectos Python\\Pruebas Empleados con importación '
   'Qoder\\build_temp\\empleados_app_v1.0.10\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'd:\\Proyectos Python\\Pruebas Empleados con importación '
   'Qoder\\build_temp\\empleados_app_v1.0.10\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'd:\\Proyectos Python\\Pruebas Empleados con importación '
   'Qoder\\build_temp\\empleados_app_v1.0.10\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'd:\\Proyectos Python\\Pruebas Empleados con importación '
   'Qoder\\build_temp\\empleados_app_v1.0.10\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('startup',
   'D:\\Proyectos Python\\Pruebas Empleados con importación Qoder\\startup.py',
   'PYSOURCE')],
 'python313.dll',
 True,
 False,
 False,
 [],
 None,
 None,
 None)
