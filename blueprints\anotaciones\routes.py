from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash
from flask_login import login_required, current_user
from datetime import datetime
from models import Empleado, db, Anotacion
from services.audit_service import AuditService
from sqlalchemy.orm import joinedload
import logging

anotaciones_bp = Blueprint('anotaciones', __name__)

@anotaciones_bp.route('/empleado/<int:empleado_id>')
@login_required
def anotaciones_empleado(empleado_id):
    """Vista de anotaciones específicas de un empleado"""
    empleado = Empleado.query.options(joinedload(Empleado.departamento_rel)).get_or_404(empleado_id)
    anotaciones = Anotacion.query.filter_by(empleado_id=empleado_id).order_by(Anotacion.fecha_creacion.desc()).all()
    
    return render_template('anotaciones/empleado.html', empleado=empleado, anotaciones=anotaciones)

@anotaciones_bp.route('/global')
@login_required
def anotaciones_global():
    """Vista global de todas las anotaciones"""
    # Filtros
    empleado_id = request.args.get('empleado_id', type=int)
    tipo = request.args.get('tipo')
    estado = request.args.get('estado')
    prioridad = request.args.get('prioridad')
    
    # Query base con join para obtener departamento
    query = Anotacion.query.join(Empleado).options(joinedload(Anotacion.empleado).joinedload(Empleado.departamento_rel))
    
    # Aplicar filtros
    if empleado_id:
        query = query.filter(Anotacion.empleado_id == empleado_id)
    if tipo:
        query = query.filter(Anotacion.tipo == tipo)
    if estado:
        query = query.filter(Anotacion.estado == estado)
    if prioridad:
        query = query.filter(Anotacion.prioridad == prioridad)
    
    anotaciones = query.order_by(Anotacion.fecha_creacion.desc()).all()
    empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.ficha).all()
    
    return render_template('anotaciones/global.html', 
                         anotaciones=anotaciones, 
                         empleados=empleados,
                         filtros={'empleado_id': empleado_id, 'tipo': tipo, 'estado': estado, 'prioridad': prioridad})

@anotaciones_bp.route('/crear', methods=['POST'])
@login_required
def crear_anotacion():
    """Crear una nueva anotación"""
    try:
        data = request.get_json()
        
        anotacion = Anotacion(
            empleado_id=data['empleado_id'],
            titulo=data['titulo'],
            descripcion=data['descripcion'],
            tipo=data['tipo'],
            prioridad=data.get('prioridad', 'normal'),
            estado=data.get('estado', 'activa'),
            fecha_inicio=datetime.fromisoformat(data['fecha_inicio']) if data.get('fecha_inicio') else None,
            fecha_fin=datetime.fromisoformat(data['fecha_fin']) if data.get('fecha_fin') else None,
            creado_por=current_user.id if hasattr(current_user, 'id') else None
        )
        
        db.session.add(anotacion)
        db.session.commit()
        
        # Registrar en auditoría
        AuditService.registrar_anotacion(
            anotacion=anotacion,
            tipo_cambio='CREAR',
            usuario=current_user
        )
        
        return jsonify({'success': True, 'id': anotacion.id, 'message': 'Anotación creada correctamente'})
        
    except Exception as e:
        db.session.rollback()
        logging.error(f"Error al crear anotación: {str(e)}")
        return jsonify({'success': False, 'message': f'Error al crear anotación: {str(e)}'}), 400

@anotaciones_bp.route('/<int:anotacion_id>', methods=['GET'])
@login_required
def obtener_anotacion(anotacion_id):
    """Obtener una anotación específica"""
    anotacion = Anotacion.query.get_or_404(anotacion_id)
    return jsonify(anotacion.to_dict())

@anotaciones_bp.route('/<int:anotacion_id>', methods=['PUT'])
@login_required
def actualizar_anotacion(anotacion_id):
    """Actualizar una anotación existente"""
    try:
        anotacion = Anotacion.query.get_or_404(anotacion_id)
        data = request.get_json()
        
        # Guardar estado anterior para auditoría
        estado_anterior = anotacion.to_dict()
        
        # Actualizar campos
        anotacion.titulo = data['titulo']
        anotacion.descripcion = data['descripcion']
        anotacion.tipo = data['tipo']
        anotacion.prioridad = data.get('prioridad', 'normal')
        anotacion.estado = data.get('estado', 'activa')
        anotacion.fecha_inicio = datetime.fromisoformat(data['fecha_inicio']) if data.get('fecha_inicio') else None
        anotacion.fecha_fin = datetime.fromisoformat(data['fecha_fin']) if data.get('fecha_fin') else None
        anotacion.modificado_por = current_user.id if hasattr(current_user, 'id') else None
        anotacion.fecha_modificacion = datetime.now()
        
        db.session.commit()
        
        # Registrar en auditoría
        AuditService.registrar_anotacion(
            anotacion=anotacion,
            tipo_cambio='ACTUALIZAR',
            estado_anterior=estado_anterior,
            usuario=current_user
        )
        
        return jsonify({'success': True, 'message': 'Anotación actualizada correctamente'})
        
    except Exception as e:
        db.session.rollback()
        logging.error(f"Error al actualizar anotación: {str(e)}")
        return jsonify({'success': False, 'message': f'Error al actualizar anotación: {str(e)}'}), 400

@anotaciones_bp.route('/<int:anotacion_id>', methods=['DELETE'])
@login_required
def eliminar_anotacion(anotacion_id):
    """Eliminar una anotación"""
    try:
        anotacion = Anotacion.query.get_or_404(anotacion_id)
        
        # Registrar en auditoría antes de eliminar
        AuditService.registrar_anotacion(
            anotacion=anotacion,
            tipo_cambio='ELIMINAR',
            usuario=current_user
        )
        
        db.session.delete(anotacion)
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'Anotación eliminada correctamente'})
        
    except Exception as e:
        db.session.rollback()
        logging.error(f"Error al eliminar anotación: {str(e)}")
        return jsonify({'success': False, 'message': f'Error al eliminar anotación: {str(e)}'}), 400

@anotaciones_bp.route('/tipos')
@login_required
def obtener_tipos():
    """Obtener tipos de anotaciones disponibles"""
    tipos = [
        {'valor': 'temporal', 'nombre': 'Temporal'},
        {'valor': 'permanente', 'nombre': 'Permanente'},
        {'valor': 'incidente', 'nombre': 'Incidente'},
        {'valor': 'observacion', 'nombre': 'Observación'}
    ]
    return jsonify(tipos)

@anotaciones_bp.route('/prioridades')
@login_required
def obtener_prioridades():
    """Obtener prioridades disponibles"""
    prioridades = [
        {'valor': 'baja', 'nombre': 'Baja'},
        {'valor': 'normal', 'nombre': 'Normal'},
        {'valor': 'alta', 'nombre': 'Alta'},
        {'valor': 'urgente', 'nombre': 'Urgente'}
    ]
    return jsonify(prioridades) 