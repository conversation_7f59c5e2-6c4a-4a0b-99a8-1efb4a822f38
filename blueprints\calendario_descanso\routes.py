from flask import render_template, request, jsonify, session, send_file, current_app
from flask_wtf.csrf import CSRFProtect
from sqlalchemy.dialects.sqlite import insert
from . import calendario_descanso_bp

# Obtener instancia de CSRF para poder usar @csrf.exempt
csrf = CSRFProtect()
from models import Empleado, Turno, Sector, CalendarioLaboral, ConfiguracionDiaLegacy, ExcepcionTurno, calendario_turno, Descanso
from database import db
from datetime import datetime
import pandas as pd
import io
import sys
import os
import traceback

# Asegurar que el directorio raíz esté en el path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Definimos los tipos de estado que puede tener un empleado en un día
TIPOS_ESTADO = ['TRABAJA', 'DESCANSO', 'VACACIONES', 'BAJA', 'PERMISO']

@calendario_descanso_bp.route('/')
def index():
    return render_template('calendario_descanso/calendario_descanso.html', tipos_estado=TIPOS_ESTADO)

@calendario_descanso_bp.route('/api/turnos')
def api_turnos():
    try:
        print("Iniciando consulta de turnos...")
        turnos = Turno.query.order_by(Turno.tipo).all()
        print(f"Número de turnos encontrados: {len(turnos)}")
        
        if not turnos:
            print("¡No se encontraron turnos en la base de datos!")
            return jsonify([])
            
        result = [{'id': t.id, 'tipo': t.tipo} for t in turnos]
        print(f"Turnos a devolver: {result}")
        return jsonify(result)
        
    except Exception as e:
        import traceback
        error_msg = f"Error en api_turnos: {str(e)}\n{traceback.format_exc()}"
        print(error_msg)
        return jsonify({"error": "Error al obtener los turnos", "details": str(e)}), 500

@calendario_descanso_bp.route('/api/sectores')
def api_sectores():
    try:
        sectores = Sector.query.order_by(Sector.nombre).all()
        return jsonify([{'id': s.id, 'nombre': s.nombre} for s in sectores])
    except Exception as e:
        print(f"Error en api_sectores: {str(e)}")
        return jsonify({"error": str(e)}), 500

@calendario_descanso_bp.route('/api/departamentos')
def api_departamentos():
    try:
        from models_py import Departamento
        departamentos = Departamento.query.order_by(Departamento.nombre).all()
        return jsonify([{'id': d.id, 'nombre': d.nombre} for d in departamentos])
    except Exception as e:
        print(f"Error en api_departamentos: {str(e)}")
        return jsonify({"error": str(e)}), 500

@calendario_descanso_bp.route('/api/consultar_descansos')
def api_consultar_descansos():
    try:
        from models_py import Empleado, Turno, Sector, Departamento
        from models.descanso import Descanso
        from sqlalchemy import and_

        turno_id = request.args.get('turno_id', type=int)
        sector_id = request.args.get('sector_id', type=int)
        departamento_ids_str = request.args.get('departamento_ids')
        fecha_desde = request.args.get('fecha_desde')
        fecha_hasta = request.args.get('fecha_hasta')

        # Procesar departamento_ids (puede ser una lista separada por comas)
        departamento_ids = []
        if departamento_ids_str:
            try:
                departamento_ids = [int(id.strip()) for id in departamento_ids_str.split(',') if id.strip()]
            except ValueError:
                pass

        # Construir query base
        # Crear alias para el sector del empleado y el sector del descanso
        SectorEmpleado = db.aliased(Sector)
        SectorDescanso = db.aliased(Sector)

        query = db.session.query(
            Descanso,
            Empleado.nombre.label('empleado_nombre'),
            Empleado.apellidos.label('empleado_apellidos'),
            Empleado.ficha.label('empleado_ficha'),
            Turno.tipo.label('turno_tipo'),
            SectorEmpleado.nombre.label('sector_nombre'),
            SectorDescanso.nombre.label('sector_descanso_nombre')
        ).join(
            Empleado, Descanso.empleado_id == Empleado.id
        ).join(
            Turno, Empleado.turno_id == Turno.id
        ).outerjoin(
            SectorEmpleado, Empleado.sector_id == SectorEmpleado.id
        ).outerjoin(
            SectorDescanso, Descanso.sector_id == SectorDescanso.id
        )

        # Aplicar filtros
        if turno_id:
            query = query.filter(Empleado.turno_id == turno_id)

        if sector_id:
            query = query.filter(Empleado.sector_id == sector_id)

        if departamento_ids:
            query = query.filter(Empleado.departamento_id.in_(departamento_ids))

        if fecha_desde:
            query = query.filter(Descanso.fecha >= fecha_desde)

        if fecha_hasta:
            query = query.filter(Descanso.fecha <= fecha_hasta)

        # Ordenar por fecha descendente
        query = query.order_by(Descanso.fecha.desc(), Empleado.apellidos, Empleado.nombre)

        resultados = query.all()

        # Formatear respuesta
        data = []
        for row in resultados:
            descanso = row[0]  # Descanso object
            # Determinar si es descanso basado en el tipo
            es_descanso = descanso.tipo in ['DESCANSO', 'VACACIONES', 'BAJA', 'PERMISO']

            data.append({
                'id': descanso.id,
                'empleado_id': descanso.empleado_id,
                'fecha': descanso.fecha.isoformat(),
                'empleado_nombre': row.empleado_nombre,
                'empleado_apellidos': row.empleado_apellidos,
                'empleado_ficha': row.empleado_ficha,
                'turno_tipo': row.turno_tipo,
                'sector_nombre': row.sector_nombre,  # Sector predeterminado del empleado
                'sector_descanso_nombre': row.sector_descanso_nombre,  # Sector asignado en el momento del descanso
                'sector_id': descanso.sector_id,  # ID del sector asignado en el momento del descanso
                'es_descanso': es_descanso,
                'tipo': descanso.tipo,
                'observaciones': descanso.observacion or '',
                'fecha_guardado': descanso.fecha_creacion.isoformat() if descanso.fecha_creacion else descanso.fecha.isoformat()
            })

        return jsonify(data)

    except Exception as e:
        current_app.logger.error(f"Error en api_consultar_descansos: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({"error": str(e)}), 500

@calendario_descanso_bp.route('/api/empleados')
def api_empleados():
    try:
        turno_id = request.args.get('turno_id', type=int)
        sector_id = request.args.get('sector_id', type=int)
        departamento_id = request.args.get('departamento_id', type=int)

        if not turno_id:
            current_app.logger.error("Error en api_empleados: No se proporcionó turno_id")
            return jsonify({"error": "Se requiere turno_id"}), 400

        current_app.logger.info(f"Buscando empleados con turno_id: {turno_id}, sector_id: {sector_id}, departamento_id: {departamento_id}")

        # Verificar si el turno existe
        turno = Turno.query.get(turno_id)
        if not turno:
            current_app.logger.error(f"Error en api_empleados: No se encontró el turno con ID {turno_id}")
            return jsonify({"error": f"No se encontró el turno con ID {turno_id}"}), 404

        # Construir query base para empleados activos con el turno especificado
        query = Empleado.query.filter_by(activo=True, turno_id=turno_id)

        # Aplicar filtros adicionales si se proporcionan
        if sector_id:
            query = query.filter_by(sector_id=sector_id)

        if departamento_id:
            query = query.filter_by(departamento_id=departamento_id)

        # Obtener empleados ordenados
        empleados = query.order_by(Empleado.apellidos, Empleado.nombre).all()
        
        current_app.logger.info(f"Se encontraron {len(empleados)} empleados con turno_id {turno_id}")
        
        # Preparar la respuesta
        resultado = []
        for empleado in empleados:
            try:
                sector_nombre = empleado.sector_rel.nombre if empleado.sector_rel else 'Sin sector'
                departamento_nombre = empleado.departamento_rel.nombre if empleado.departamento_rel else 'Sin departamento'
                resultado.append({
                    'id': empleado.id,
                    'ficha': empleado.ficha,
                    'nombre': empleado.nombre,
                    'apellidos': empleado.apellidos,
                    'cargo': empleado.cargo or 'Sin cargo',
                    'sector_id': empleado.sector_id,
                    'sector_nombre': sector_nombre,
                    'departamento_id': empleado.departamento_id,
                    'departamento_nombre': departamento_nombre
                })
            except Exception as e:
                current_app.logger.error(f"Error procesando empleado ID {empleado.id}: {str(e)}")
                current_app.logger.error(traceback.format_exc())
                continue
        
        return jsonify(resultado)
        
    except Exception as e:
        error_msg = f"Error en api_empleados: {str(e)}\n{traceback.format_exc()}"
        current_app.logger.error(error_msg)
        return jsonify({"error": "Error al obtener los empleados", "details": str(e)}), 500

@calendario_descanso_bp.route('/api/descansos')
def api_descansos():
    try:
        turno_id = request.args.get('turno_id', type=int)
        fecha_inicio_str = request.args.get('fecha_inicio')
        fecha_fin_str = request.args.get('fecha_fin')

        current_app.logger.info(f"Solicitando descansos para turno_id={turno_id}, fecha_inicio={fecha_inicio_str}, fecha_fin={fecha_fin_str}")

        if not all([turno_id, fecha_inicio_str, fecha_fin_str]):
            error_msg = f"Faltan parámetros requeridos. turno_id: {turno_id}, fecha_inicio: {fecha_inicio_str}, fecha_fin: {fecha_fin_str}"
            current_app.logger.error(error_msg)
            return jsonify({"error": error_msg}), 400

        try:
            fecha_inicio = datetime.strptime(fecha_inicio_str, '%Y-%m-%d').date()
            fecha_fin = datetime.strptime(fecha_fin_str, '%Y-%m-%d').date()
        except ValueError as e:
            error_msg = f"Error al convertir fechas: {str(e)}"
            current_app.logger.error(error_msg)
            return jsonify({"error": error_msg}), 400

        current_app.logger.info(f"Buscando descansos para turno_id={turno_id} entre {fecha_inicio} y {fecha_fin}")

        try:
            # Verificar si el turno existe
            turno = Turno.query.get(turno_id)
            if not turno:
                error_msg = f"No se encontró el turno con ID {turno_id}"
                current_app.logger.error(error_msg)
                return jsonify({"error": error_msg}), 404

            # Obtener los descansos para el rango de fechas
            descansos = db.session.query(Descanso).join(Empleado).filter(
                Empleado.turno_id == turno_id,
                Descanso.fecha.between(fecha_inicio, fecha_fin)
            ).all()

            current_app.logger.info(f"Se encontraron {len(descansos)} descansos")

            # Preparar la respuesta
            resultado = [
                {
                    'empleado_id': d.empleado_id,
                    'fecha': d.fecha.isoformat(),
                    'tipo': d.tipo,
                    'observacion': d.observacion if d.observacion else ''
                }
                for d in descansos
            ]

            return jsonify(resultado)

        except Exception as e:
            error_msg = f"Error al consultar la base de datos: {str(e)}\n{traceback.format_exc()}"
            current_app.logger.error(error_msg)
            return jsonify({"error": "Error al consultar la base de datos", "details": str(e)}), 500

    except Exception as e:
        error_msg = f"Error inesperado en /api/descansos: {str(e)}\n{traceback.format_exc()}"
        current_app.logger.error(error_msg)
        return jsonify({"error": "Error interno del servidor", "details": str(e)}), 500

@calendario_descanso_bp.route('/api/asignar_descanso_masivo', methods=['POST'])
def api_asignar_descanso_masivo():
    data = request.json
    turno_id = data.get('turno_id')
    fecha_str = data.get('fecha')
    creado_por = session.get('user_id', 'sistema')

    if not turno_id or not fecha_str:
        return jsonify({"error": "Faltan datos para la asignación masiva"}), 400

    fecha = datetime.strptime(fecha_str, '%Y-%m-%d').date()
    empleados_del_turno = Empleado.query.filter_by(turno_id=turno_id, activo=True).all()

    try:
        for empleado in empleados_del_turno:
            stmt = insert(Descanso).values(
                empleado_id=empleado.id,
                fecha=fecha,
                tipo='DESCANSO',
                creado_por=creado_por
            )
            stmt = stmt.on_conflict_do_update(
                index_elements=['empleado_id', 'fecha'],
                set_=dict(tipo='DESCANSO', creado_por=creado_por, fecha_modificacion=datetime.now())
            )
            db.session.execute(stmt)
        
        db.session.commit()
        return jsonify({'ok': True, 'message': f'{len(empleados_del_turno)} descansos asignados.'})
    except Exception as e:
        db.session.rollback()
        print(f"Error en asignación masiva: {e}")
        return jsonify({"error": "No se pudo completar la asignación masiva"}), 500

@calendario_descanso_bp.route('/api/guardar_descansos', methods=['POST'])
def api_guardar_descansos():
    try:
        # Validar que el request tiene JSON
        if not request.json:
            current_app.logger.error("❌ No se recibió JSON en la petición")
            return jsonify({"error": "No se recibieron datos JSON"}), 400

        data = request.json.get('descansos', [])
        creado_por = session.get('user_id', 'sistema')

        current_app.logger.info(f"🔧 Guardando {len(data)} descansos")

        if not data:
            current_app.logger.error("❌ Lista de descansos vacía")
            return jsonify({"error": "No se recibieron descansos para guardar"}), 400
        for i, d in enumerate(data):
            try:
                current_app.logger.info(f"📝 Procesando descanso {i+1}/{len(data)}: {d}")

                fecha = datetime.strptime(d['fecha'], '%Y-%m-%d').date()
                empleado_id = d['empleado_id']
                tipo = d.get('tipo', 'TRABAJA')

                if tipo == 'TRABAJA':
                    # Si es 'TRABAJA', eliminamos el registro para no saturar la tabla
                    current_app.logger.info(f"🗑️ Eliminando registro TRABAJA para empleado {empleado_id}, fecha {fecha}")
                    Descanso.query.filter_by(empleado_id=empleado_id, fecha=fecha).delete()
                else:
                    current_app.logger.info(f"💾 Guardando descanso {tipo} para empleado {empleado_id}, fecha {fecha}")

                    # Buscar si ya existe un registro
                    descanso_existente = Descanso.query.filter_by(empleado_id=empleado_id, fecha=fecha).first()

                    if descanso_existente:
                        # Actualizar registro existente
                        current_app.logger.info(f"🔄 Actualizando registro existente ID {descanso_existente.id}")
                        descanso_existente.tipo = tipo
                        descanso_existente.observacion = d.get('observacion', '')
                        descanso_existente.sector_id = d.get('sector_id')
                        descanso_existente.fecha_modificacion = datetime.now()
                    else:
                        # Crear nuevo registro
                        current_app.logger.info(f"➕ Creando nuevo registro")
                        nuevo_descanso = Descanso(
                            empleado_id=empleado_id,
                            fecha=fecha,
                            tipo=tipo,
                            observacion=d.get('observacion', ''),
                            sector_id=d.get('sector_id'),
                            creado_por=creado_por
                        )
                        db.session.add(nuevo_descanso)

            except Exception as e:
                current_app.logger.error(f"❌ Error procesando descanso {i+1}: {e}")
                raise e

        db.session.commit()
        current_app.logger.info("✅ Descansos guardados correctamente")
        return jsonify({'ok': True, 'message': 'Cambios guardados correctamente.'})
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"❌ Error al guardar descansos: {e}")
        import traceback
        current_app.logger.error(f"❌ Stack trace: {traceback.format_exc()}")
        return jsonify({"error": f"No se pudieron guardar los cambios: {str(e)}"}), 500

@calendario_descanso_bp.route('/api/eliminar_descanso', methods=['DELETE'])
def api_eliminar_descanso():
    try:
        empleado_id = request.args.get('empleado_id', type=int)
        fecha = request.args.get('fecha')

        if not empleado_id or not fecha:
            return jsonify({"error": "empleado_id y fecha son requeridos"}), 400

        fecha_obj = datetime.strptime(fecha, '%Y-%m-%d').date()

        # Buscar y eliminar el registro
        descanso = Descanso.query.filter_by(empleado_id=empleado_id, fecha=fecha_obj).first()
        if descanso:
            db.session.delete(descanso)
            db.session.commit()
            return jsonify({'ok': True, 'message': 'Registro eliminado correctamente'})
        else:
            return jsonify({"error": "Registro no encontrado"}), 404

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error al eliminar descanso: {str(e)}")
        return jsonify({"error": "Error al eliminar el registro"}), 500

@calendario_descanso_bp.route('/api/editar_descanso', methods=['PUT'])
def api_editar_descanso():
    try:
        data = request.json
        empleado_id = data.get('empleado_id')
        fecha_original = data.get('fecha_original')  # Fecha original del registro
        fecha_nueva = data.get('fecha_nueva')       # Nueva fecha seleccionada
        nuevo_tipo = data.get('tipo')
        nueva_observacion = data.get('observacion', '')
        nuevo_sector_id = data.get('sector_id')  # Sector asignado en el momento del descanso

        # Usar fecha_nueva si está disponible, sino usar fecha_original (compatibilidad)
        fecha_a_usar = fecha_nueva if fecha_nueva else fecha_original

        if not empleado_id or not fecha_original or not nuevo_tipo:
            return jsonify({"error": "empleado_id, fecha_original y tipo son requeridos"}), 400

        fecha_original_obj = datetime.strptime(fecha_original, '%Y-%m-%d').date()
        fecha_nueva_obj = datetime.strptime(fecha_a_usar, '%Y-%m-%d').date()

        # Buscar el registro original
        descanso = Descanso.query.filter_by(empleado_id=empleado_id, fecha=fecha_original_obj).first()

        if descanso:
            # Si la fecha cambió, verificar que no exista ya un registro en la nueva fecha
            if fecha_nueva and fecha_original != fecha_nueva:
                registro_existente = Descanso.query.filter_by(
                    empleado_id=empleado_id,
                    fecha=fecha_nueva_obj
                ).first()

                if registro_existente:
                    return jsonify({
                        "error": f"Ya existe un registro para el empleado en la fecha {fecha_nueva}"
                    }), 400

                # Actualizar la fecha del registro
                descanso.fecha = fecha_nueva_obj

            # Actualizar el tipo, observación y sector
            descanso.tipo = nuevo_tipo
            descanso.observacion = nueva_observacion
            descanso.sector_id = nuevo_sector_id if nuevo_sector_id else None

            db.session.commit()
            return jsonify({'ok': True, 'message': 'Registro actualizado correctamente'})
        else:
            return jsonify({"error": "Registro no encontrado"}), 404

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error al editar descanso: {str(e)}")
        import traceback
        current_app.logger.error(traceback.format_exc())
        return jsonify({"error": "Error al actualizar el registro"}), 500

@calendario_descanso_bp.route('/empleado/<int:empleado_id>')
def detalle_empleado(empleado_id):
    try:
        current_app.logger.info(f"Cargando detalle del empleado ID: {empleado_id}")

        # Cargar empleado básico
        empleado = Empleado.query.get_or_404(empleado_id)
        current_app.logger.info(f"Empleado encontrado: {empleado.nombre} {empleado.apellidos}")

        return render_template('calendario_descanso/detalle_empleado.html', empleado=empleado)
    except Exception as e:
        current_app.logger.error(f"Error al cargar detalle empleado: {str(e)}")
        import traceback
        current_app.logger.error(f"Traceback: {traceback.format_exc()}")
        return "Error interno del servidor", 500

@calendario_descanso_bp.route('/api/historial_empleado/<int:empleado_id>')
def api_historial_empleado(empleado_id):
    try:
        from models_py import Empleado, Turno, Sector, Departamento
        from models.descanso import Descanso

        # Verificar que el empleado existe
        empleado = Empleado.query.get_or_404(empleado_id)

        # Obtener parámetros de filtro
        fecha_desde = request.args.get('fecha_desde')
        fecha_hasta = request.args.get('fecha_hasta')

        # Construir query
        query = db.session.query(
            Descanso,
            Turno.tipo.label('turno_tipo'),
            Sector.nombre.label('sector_nombre'),
            Departamento.nombre.label('departamento_nombre')
        ).join(
            Empleado, Descanso.empleado_id == Empleado.id
        ).join(
            Turno, Empleado.turno_id == Turno.id
        ).outerjoin(
            Sector, Empleado.sector_id == Sector.id
        ).outerjoin(
            Departamento, Empleado.departamento_id == Departamento.id
        ).filter(
            Descanso.empleado_id == empleado_id
        )

        # Aplicar filtros de fecha
        if fecha_desde:
            query = query.filter(Descanso.fecha >= fecha_desde)
        if fecha_hasta:
            query = query.filter(Descanso.fecha <= fecha_hasta)

        # Ordenar por fecha descendente
        query = query.order_by(Descanso.fecha.desc())

        resultados = query.all()

        # Formatear respuesta
        data = []
        for row in resultados:
            descanso = row[0]
            es_descanso = descanso.tipo in ['DESCANSO', 'VACACIONES', 'BAJA', 'PERMISO']

            data.append({
                'id': descanso.id,
                'fecha': descanso.fecha.isoformat(),
                'tipo': descanso.tipo,
                'es_descanso': es_descanso,
                'observacion': descanso.observacion or '',
                'turno_tipo': row.turno_tipo,
                'sector_nombre': row.sector_nombre,
                'departamento_nombre': row.departamento_nombre,
                'fecha_creacion': descanso.fecha_creacion.isoformat() if descanso.fecha_creacion else None,
                'creado_por': descanso.creado_por
            })

        return jsonify({
            'empleado': {
                'id': empleado.id,
                'nombre': empleado.nombre,
                'apellidos': empleado.apellidos,
                'ficha': empleado.ficha
            },
            'historial': data
        })

    except Exception as e:
        current_app.logger.error(f"Error al obtener historial: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({"error": str(e)}), 500

@calendario_descanso_bp.route('/api/exportar_excel', methods=['POST'])
def api_exportar_excel():
    try:
        data = request.json
        semana = data.get('semana')  # Semana única (compatibilidad)
        semana_desde = data.get('semana_desde')  # Semana inicial del rango
        semana_hasta = data.get('semana_hasta')  # Semana final del rango
        turno_id = data.get('turno_id')
        empleados_seleccionados = data.get('empleados_seleccionados')  # Lista de IDs de empleados marcados
        sector_id = data.get('sector_id')  # Filtro de sector
        departamento_ids = data.get('departamento_ids')  # Lista de IDs de departamentos

        # Determinar si es exportación de una semana o rango
        if semana_desde and semana_hasta:
            # Exportación por rango
            semanas_a_exportar = generar_lista_semanas(semana_desde, semana_hasta)
            if not semanas_a_exportar:
                return jsonify({"error": "Rango de semanas inválido"}), 400
        elif semana:
            # Exportación de una sola semana (compatibilidad)
            semanas_a_exportar = [semana]
        else:
            return jsonify({"error": "Debe especificar 'semana' o rango 'semana_desde' y 'semana_hasta'"}), 400

        if not turno_id:
            return jsonify({"error": "Turno es requerido"}), 400

        # Crear workbook para múltiples semanas
        from openpyxl import Workbook
        wb = Workbook()

        # Eliminar la hoja por defecto
        wb.remove(wb.active)

        # Procesar cada semana
        for i, semana_actual in enumerate(semanas_a_exportar):
            # Parsear semana (formato: 2025-W31)
            year, week_num = semana_actual.split('-W')
            year = int(year)
            week_num = int(week_num)

            # Calcular fechas de la semana usando el mismo método que el frontend
            from datetime import date, timedelta

            # Enero 4 siempre está en la semana 1 según ISO 8601
            jan4 = date(year, 1, 4)

            # Encontrar el lunes de la semana 1
            monday_week1 = jan4 - timedelta(days=(jan4.weekday()))

            # Calcular el lunes de la semana deseada
            monday_target = monday_week1 + timedelta(weeks=week_num - 1)

            # Generar las 7 fechas de la semana
            week_dates = [monday_target + timedelta(days=i) for i in range(7)]

            # Obtener datos de empleados y descansos (solo en la primera iteración)
            if i == 0:
                from models_py import Empleado, Turno, Sector
                from models.descanso import Descanso

                # CAMBIO IMPORTANTE: Primero obtener empleados que tienen descansos en el período
                # Calcular rango completo de fechas para todas las semanas
                fecha_inicio_total = None
                fecha_fin_total = None

                for semana_temp in semanas_a_exportar:
                    year_temp, week_num_temp = semana_temp.split('-W')
                    year_temp = int(year_temp)
                    week_num_temp = int(week_num_temp)

                    jan4_temp = date(year_temp, 1, 4)
                    monday_week1_temp = jan4_temp - timedelta(days=(jan4_temp.weekday()))
                    monday_target_temp = monday_week1_temp + timedelta(weeks=week_num_temp - 1)
                    week_dates_temp = [monday_target_temp + timedelta(days=j) for j in range(7)]

                    if fecha_inicio_total is None or week_dates_temp[0] < fecha_inicio_total:
                        fecha_inicio_total = week_dates_temp[0]
                    if fecha_fin_total is None or week_dates_temp[6] > fecha_fin_total:
                        fecha_fin_total = week_dates_temp[6]

                # Obtener empleados que tienen descansos en el período Y pertenecen al turno
                empleados_con_descansos_query = db.session.query(Empleado).join(Descanso).filter(
                    Empleado.turno_id == turno_id,
                    Empleado.activo == True,
                    Descanso.fecha.between(fecha_inicio_total, fecha_fin_total)
                ).distinct()

                # Aplicar filtros adicionales
                if sector_id:
                    empleados_con_descansos_query = empleados_con_descansos_query.filter(Empleado.sector_id == sector_id)

                if departamento_ids:
                    empleados_con_descansos_query = empleados_con_descansos_query.filter(Empleado.departamento_id.in_(departamento_ids))

                # Si se especifican empleados seleccionados, filtrar solo esos
                if empleados_seleccionados:
                    empleados_con_descansos_query = empleados_con_descansos_query.filter(Empleado.id.in_(empleados_seleccionados))

                empleados = empleados_con_descansos_query.order_by(Empleado.nombre, Empleado.apellidos).all()

                if not empleados:
                    return jsonify({"error": "No se encontraron empleados con descansos asignados para el período especificado"}), 404

            # Obtener descansos de la semana actual
            fecha_inicio = week_dates[0]
            fecha_fin = week_dates[6]

            descansos = db.session.query(Descanso).join(Empleado).filter(
                Empleado.turno_id == turno_id,
                Empleado.activo == True,
                Descanso.fecha.between(fecha_inicio, fecha_fin)
            ).all()

            # Crear diccionario de descansos por empleado y fecha
            descansos_dict = {}
            for descanso in descansos:
                key = f"{descanso.empleado_id}_{descanso.fecha}"
                descansos_dict[key] = descanso.tipo

            # Crear nueva hoja para esta semana (solo en la primera iteración crear workbook)
            if i == 0:
                import pandas as pd
                import io
                from openpyxl import Workbook
                wb = Workbook()
                # Eliminar la hoja por defecto
                wb.remove(wb.active)

            # Crear nueva hoja para esta semana
            ws = wb.create_sheet(title=f"Semana {week_num}")

            # Preparar datos en el formato esperado por generar_hoja_semana
            empleados_dict = {}
            for empleado in empleados:
                # Buscar el sector asignado en los descansos de esta semana
                sector_asignado = None
                for descanso in descansos:
                    if descanso.empleado_id == empleado.id and hasattr(descanso, 'sector_id') and descanso.sector_id:
                        # Obtener el nombre del sector asignado
                        from models_py import Sector
                        sector_obj = db.session.query(Sector).filter_by(id=descanso.sector_id).first()
                        if sector_obj:
                            sector_asignado = sector_obj.nombre
                            break

                # Si no hay sector asignado, usar el predefinido del empleado
                if not sector_asignado:
                    sector_asignado = empleado.sector_rel.nombre if empleado.sector_rel else "N/A"

                empleados_dict[empleado.id] = {
                    'nombre': f"{empleado.nombre} {empleado.apellidos}",
                    'sector': sector_asignado
                }

            semana_info = {
                'week_num': week_num,
                'year': year,
                'dates': week_dates,
                'empleados': empleados_dict,
                'descansos': descansos_dict
            }

            # Usar la función común para generar el formato
            generar_hoja_semana(ws, semana_info)

        # Guardar en BytesIO
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        # Generar nombre de archivo apropiado
        if len(semanas_a_exportar) == 1:
            # Una sola semana
            year, week_num = semanas_a_exportar[0].split('-W')
            filename = f"calendario_semana_{week_num}_{year}.xlsx"
        else:
            # Rango de semanas
            semana_inicio = semanas_a_exportar[0].split('-W')[1]
            semana_fin = semanas_a_exportar[-1].split('-W')[1]
            year = semanas_a_exportar[0].split('-W')[0]
            filename = f"calendario_semanas_{semana_inicio}-{semana_fin}_{year}.xlsx"

        return send_file(
            output,
            download_name=filename,
            as_attachment=True,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        current_app.logger.error(f"Error al exportar Excel: {str(e)}")
        import traceback
        current_app.logger.error(traceback.format_exc())
        return jsonify({"error": str(e)}), 500

@calendario_descanso_bp.route('/api/exportar_pdf', methods=['POST'])
@csrf.exempt
def api_exportar_pdf():
    try:
        # Validar que el request tiene JSON
        if not request.json:
            current_app.logger.error("❌ No se recibió JSON en la petición PDF")
            return jsonify({"error": "No se recibieron datos JSON"}), 400

        data = request.json
        current_app.logger.info(f"📄 Datos recibidos para exportación PDF: {data}")

        semana = data.get('semana')  # Semana única (compatibilidad)
        semana_desde = data.get('semana_desde')  # Semana inicial del rango
        semana_hasta = data.get('semana_hasta')  # Semana final del rango
        turno_id = data.get('turno_id')
        empleados_seleccionados = data.get('empleados_seleccionados')  # Lista de IDs de empleados marcados
        sector_id = data.get('sector_id')  # Filtro de sector
        departamento_ids = data.get('departamento_ids')  # Lista de IDs de departamentos

        # Determinar si es exportación de una semana o rango
        if semana_desde and semana_hasta:
            # Exportación por rango
            current_app.logger.info(f"📄 Exportación por rango: {semana_desde} - {semana_hasta}")
            semanas_a_exportar = generar_lista_semanas(semana_desde, semana_hasta)
            if not semanas_a_exportar:
                current_app.logger.error("❌ Rango de semanas inválido")
                return jsonify({"error": "Rango de semanas inválido"}), 400
        elif semana:
            # Exportación de una sola semana (compatibilidad)
            current_app.logger.info(f"📄 Exportación de semana única: {semana}")
            semanas_a_exportar = [semana]
        else:
            current_app.logger.error("❌ No se especificó semana ni rango")
            return jsonify({"error": "Debe especificar 'semana' o rango 'semana_desde' y 'semana_hasta'"}), 400

        if not turno_id:
            current_app.logger.error("❌ Turno no especificado")
            return jsonify({"error": "Turno es requerido"}), 400

        current_app.logger.info(f"📄 Turno: {turno_id}, Semanas: {len(semanas_a_exportar)}")

        # Generar PDF
        pdf_buffer = generar_pdf_calendario(
            semanas_a_exportar,
            turno_id,
            empleados_seleccionados,
            sector_id,
            departamento_ids
        )

        # Generar nombre de archivo apropiado
        if len(semanas_a_exportar) == 1:
            # Una sola semana
            year, week_num = semanas_a_exportar[0].split('-W')
            filename = f"calendario_semana_{week_num}_{year}.pdf"
        else:
            # Rango de semanas
            semana_inicio = semanas_a_exportar[0].split('-W')[1]
            semana_fin = semanas_a_exportar[-1].split('-W')[1]
            year = semanas_a_exportar[0].split('-W')[0]
            filename = f"calendario_semanas_{semana_inicio}-{semana_fin}_{year}.pdf"

        return send_file(
            pdf_buffer,
            download_name=filename,
            as_attachment=True,
            mimetype='application/pdf'
        )

    except Exception as e:
        current_app.logger.error(f"Error al exportar PDF: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({"error": str(e)}), 500


def generar_lista_semanas(semana_desde, semana_hasta):
    """Generar lista de semanas desde semana_desde hasta semana_hasta"""
    try:
        # Parsear semanas
        year_desde, week_desde = semana_desde.split('-W')
        year_hasta, week_hasta = semana_hasta.split('-W')

        year_desde = int(year_desde)
        week_desde = int(week_desde)
        year_hasta = int(year_hasta)
        week_hasta = int(week_hasta)

        semanas = []

        # Si es el mismo año
        if year_desde == year_hasta:
            if week_desde <= week_hasta:
                for week in range(week_desde, week_hasta + 1):
                    semanas.append(f"{year_desde}-W{week:02d}")
            else:
                return None  # Rango inválido
        else:
            # Diferentes años - por simplicidad, limitamos a un año
            return None

        return semanas if semanas else None

    except Exception:
        return None


def generar_hoja_semana(ws, semana_info):
    """Generar formato específico de hoja Excel basado en las imágenes"""
    from openpyxl.styles import PatternFill, Font, Alignment, Border, Side

    week_num = semana_info['week_num']
    year = semana_info['year']
    week_dates = semana_info['dates']
    empleados = semana_info['empleados']
    descansos = semana_info['descansos']

    # Colores específicos del formato (basados en las imágenes)
    color_header = "87CEEB"    # Azul claro para SEMANA
    color_month_julio = "DDA0DD"   # Lila para JULIO
    color_month_agosto = "DDA0DD"  # Lila para AGOSTO
    color_name = "DEB887"      # Beige para NOMBRE
    color_descanso = "000000"  # Negro para descansos

    # Meses en español
    meses = ["", "ENERO", "FEBRERO", "MARZO", "ABRIL", "MAYO", "JUNIO",
            "JULIO", "AGOSTO", "SEPTIEMBRE", "OCTUBRE", "NOVIEMBRE", "DICIEMBRE"]

    # 1. Encabezado principal "SEMANA XX" (basado en imagen)
    ws.merge_cells('A1:I1')  # Extender hasta columna I
    ws['A1'] = f"SEMANA {week_num}"
    ws['A1'].font = Font(bold=True, size=14, color="000000")
    ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
    ws['A1'].fill = PatternFill(start_color=color_header, end_color=color_header, fill_type="solid")

    # 2. Fila de meses (JULIO y AGOSTO según imagen)
    # Determinar qué meses aparecen en la semana
    meses_en_semana = set(fecha.month for fecha in week_dates)

    if 7 in meses_en_semana:  # Julio
        # Encontrar columnas de julio
        julio_cols = [i for i, fecha in enumerate(week_dates) if fecha.month == 7]
        if julio_cols:
            start_col = 3 + julio_cols[0]
            end_col = 3 + julio_cols[-1]
            if start_col == end_col:
                ws.cell(row=2, column=start_col, value="JULIO")
            else:
                ws.merge_cells(start_row=2, start_column=start_col, end_row=2, end_column=end_col)
                ws.cell(row=2, column=start_col, value="JULIO")
            ws.cell(row=2, column=start_col).font = Font(bold=True, color="000000")
            ws.cell(row=2, column=start_col).fill = PatternFill(start_color=color_month_julio, end_color=color_month_julio, fill_type="solid")
            ws.cell(row=2, column=start_col).alignment = Alignment(horizontal='center', vertical='center')

    if 8 in meses_en_semana:  # Agosto
        # Encontrar columnas de agosto
        agosto_cols = [i for i, fecha in enumerate(week_dates) if fecha.month == 8]
        if agosto_cols:
            start_col = 3 + agosto_cols[0]
            end_col = 3 + agosto_cols[-1]
            if start_col == end_col:
                ws.cell(row=2, column=start_col, value="AGOSTO")
            else:
                ws.merge_cells(start_row=2, start_column=start_col, end_row=2, end_column=end_col)
                ws.cell(row=2, column=start_col, value="AGOSTO")
            ws.cell(row=2, column=start_col).font = Font(bold=True, color="000000")
            ws.cell(row=2, column=start_col).fill = PatternFill(start_color=color_month_agosto, end_color=color_month_agosto, fill_type="solid")
            ws.cell(row=2, column=start_col).alignment = Alignment(horizontal='center', vertical='center')

    # 3. Fila de días
    for i, fecha in enumerate(week_dates):
        col = 3 + i
        ws.cell(row=3, column=col, value=fecha.day)
        ws.cell(row=3, column=col).font = Font(bold=True, color="000000")
        ws.cell(row=3, column=col).alignment = Alignment(horizontal='center', vertical='center')

    # 4. Encabezados de columnas (SECTOR primero, luego NOMBRE)
    ws['A2'] = "SECTOR"
    ws['A2'].font = Font(bold=True, color="000000")
    ws['A2'].fill = PatternFill(start_color=color_name, end_color=color_name, fill_type="solid")
    ws['A2'].alignment = Alignment(horizontal='center', vertical='center')

    ws['B2'] = "NOMBRE"
    ws['B2'].font = Font(bold=True, color="000000")
    ws['B2'].fill = PatternFill(start_color=color_name, end_color=color_name, fill_type="solid")
    ws['B2'].alignment = Alignment(horizontal='center', vertical='center')

    ws['A3'] = ""  # Fila 3 vacía para encabezados
    ws['B3'] = ""

    # 5. Llenar datos de empleados (SECTOR primero, NOMBRE en negrita)
    row = 4

    # Listar empleados ordenados por sector, luego por nombre
    empleados_ordenados = sorted(empleados.items(), key=lambda x: (x[1]['sector'], x[1]['nombre']))

    for emp_id, emp_data in empleados_ordenados:
        # Sector en columna A
        ws.cell(row=row, column=1, value=emp_data['sector'])
        ws.cell(row=row, column=1).font = Font(bold=False, color="000000")

        # Nombre del empleado en columna B (EN NEGRITA)
        ws.cell(row=row, column=2, value=emp_data['nombre'])
        ws.cell(row=row, column=2).font = Font(bold=True, color="000000")

        # Datos de cada día (columnas C-I)
        for i, fecha in enumerate(week_dates):
            col = 3 + i
            key = f"{emp_id}_{fecha}"

            cell = ws.cell(row=row, column=col)

            if key in descansos:
                tipo = descansos[key]
                if tipo == 'DESCANSO':
                    # Celda negra para descansos (según imagen)
                    cell.fill = PatternFill(start_color=color_descanso, end_color=color_descanso, fill_type="solid")
                elif tipo == 'VACACIONES':
                    # Celda amarilla para vacaciones
                    cell.fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
                elif tipo in ['BAJA', 'PERMISO']:
                    # Celda gris para bajas/permisos
                    cell.fill = PatternFill(start_color="808080", end_color="808080", fill_type="solid")

        row += 1

    # 6. Aplicar bordes a toda la tabla
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # Aplicar bordes solo al área con datos
    for row_cells in ws.iter_rows(min_row=1, max_row=row-1, min_col=1, max_col=9):
        for cell in row_cells:
            cell.border = thin_border

    # 7. Ajustar anchos de columna automáticamente
    from openpyxl.utils import get_column_letter

    # Autoajustar columnas A y B basándose en el contenido
    max_width_sector = max(len(str(ws.cell(row=r, column=1).value or "")) for r in range(2, row)) + 2
    max_width_nombre = max(len(str(ws.cell(row=r, column=2).value or "")) for r in range(2, row)) + 2

    ws.column_dimensions['A'].width = min(max_width_sector, 20)  # SECTOR (máximo 20)
    ws.column_dimensions['B'].width = min(max_width_nombre, 35)  # NOMBRE (máximo 35)

    # Días de la semana (C-I) - ancho fijo pequeño
    for i in range(7):
        col_letter = chr(ord('C') + i)
        ws.column_dimensions[col_letter].width = 6

def generar_pdf_calendario(semanas_a_exportar, turno_id, empleados_seleccionados, sector_id, departamento_ids):
    """
    Genera un PDF con el calendario de descansos para las semanas especificadas.
    """
    try:
        # Importaciones con manejo de errores
        try:
            from reportlab.lib import colors
            from reportlab.lib.pagesizes import A4, landscape
            from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet
            from reportlab.lib.units import inch
            current_app.logger.info("📄 ReportLab importado correctamente")
        except ImportError as e:
            current_app.logger.error(f"❌ Error importando ReportLab: {e}")
            raise Exception(f"ReportLab no está instalado correctamente: {e}")

        import io
        from datetime import date, timedelta

        current_app.logger.info(f"🔧 Iniciando generación PDF para {len(semanas_a_exportar)} semanas, turno {turno_id}")

        # Crear buffer para el PDF
        buffer = io.BytesIO()
        current_app.logger.info("📄 Buffer creado")

        # Configurar documento en orientación horizontal
        try:
            doc = SimpleDocTemplate(buffer, pagesize=landscape(A4),
                                  rightMargin=0.5*inch, leftMargin=0.5*inch,
                                  topMargin=0.5*inch, bottomMargin=0.5*inch)
            current_app.logger.info("📄 Documento configurado")
        except Exception as e:
            current_app.logger.error(f"❌ Error configurando documento: {e}")
            raise e

        # Estilos básicos
        try:
            styles = getSampleStyleSheet()
            current_app.logger.info("📄 Estilos obtenidos")
        except Exception as e:
            current_app.logger.error(f"❌ Error obteniendo estilos: {e}")
            raise e

        # Contenido del documento
        story = []

        # Obtener datos de empleados (solo una vez)
        current_app.logger.info("📋 Importando modelos...")
        from models_py import Empleado, Turno, Sector
        from models.descanso import Descanso
        current_app.logger.info("📋 Modelos importados")

        # CAMBIO IMPORTANTE: Obtener solo empleados que tienen descansos en el período
        current_app.logger.info(f"🔍 Buscando empleados con descansos para turno {turno_id}")

        # Calcular rango completo de fechas para todas las semanas
        fecha_inicio_total = None
        fecha_fin_total = None

        for semana_temp in semanas_a_exportar:
            year_temp, week_num_temp = semana_temp.split('-W')
            year_temp = int(year_temp)
            week_num_temp = int(week_num_temp)

            jan4_temp = date(year_temp, 1, 4)
            monday_week1_temp = jan4_temp - timedelta(days=(jan4_temp.weekday()))
            monday_target_temp = monday_week1_temp + timedelta(weeks=week_num_temp - 1)
            week_dates_temp = [monday_target_temp + timedelta(days=j) for j in range(7)]

            if fecha_inicio_total is None or week_dates_temp[0] < fecha_inicio_total:
                fecha_inicio_total = week_dates_temp[0]
            if fecha_fin_total is None or week_dates_temp[6] > fecha_fin_total:
                fecha_fin_total = week_dates_temp[6]

        current_app.logger.info(f"📅 Rango de fechas: {fecha_inicio_total} - {fecha_fin_total}")

        # Obtener empleados que tienen descansos en el período Y pertenecen al turno
        query = db.session.query(Empleado).join(Descanso).filter(
            Empleado.turno_id == turno_id,
            Empleado.activo == True,
            Descanso.fecha.between(fecha_inicio_total, fecha_fin_total)
        ).distinct()

        # Aplicar filtros de sector y departamento
        if sector_id:
            query = query.filter(Empleado.sector_id == sector_id)
            current_app.logger.info(f"🔍 Filtro sector aplicado: {sector_id}")

        if departamento_ids:
            query = query.filter(Empleado.departamento_id.in_(departamento_ids))
            current_app.logger.info(f"🔍 Filtro departamentos aplicado: {departamento_ids}")

        # Si se especifican empleados seleccionados, filtrar solo esos
        if empleados_seleccionados:
            query = query.filter(Empleado.id.in_(empleados_seleccionados))
            current_app.logger.info(f"🔍 Empleados seleccionados: {len(empleados_seleccionados)}")

        empleados = query.order_by(Empleado.nombre, Empleado.apellidos).all()
        current_app.logger.info(f"👥 Empleados con descansos encontrados: {len(empleados)}")

        if not empleados:
            current_app.logger.error("❌ No se encontraron empleados con descansos")
            raise Exception("No se encontraron empleados con descansos asignados para el período especificado")

        # Obtener información del turno
        current_app.logger.info(f"🔍 Obteniendo información del turno {turno_id}")
        turno = db.session.query(Turno).filter_by(id=turno_id).first()
        turno_nombre = turno.tipo if turno else f"Turno {turno_id}"
        current_app.logger.info(f"📋 Turno: {turno_nombre}")

        # Título principal
        if len(semanas_a_exportar) == 1:
            year, week_num = semanas_a_exportar[0].split('-W')
            title = f"Calendario de Descansos - Semana {week_num}/{year}"
        else:
            semana_inicio = semanas_a_exportar[0].split('-W')[1]
            semana_fin = semanas_a_exportar[-1].split('-W')[1]
            year = semanas_a_exportar[0].split('-W')[0]
            title = f"Calendario de Descansos - Semanas {semana_inicio}-{semana_fin}/{year}"

        current_app.logger.info(f"📄 Título: {title}")

        story.append(Paragraph(title, title_style))
        story.append(Paragraph(f"Turno: {turno_nombre}", subtitle_style))
        story.append(Spacer(1, 20))
        current_app.logger.info("📄 Encabezados agregados")

        # Procesar cada semana
        for i, semana_actual in enumerate(semanas_a_exportar):
            if i > 0:  # Agregar salto de página entre semanas
                story.append(PageBreak())

            # Parsear semana
            year, week_num = semana_actual.split('-W')
            year = int(year)
            week_num = int(week_num)

            # Calcular fechas de la semana
            jan4 = date(year, 1, 4)
            monday_week1 = jan4 - timedelta(days=(jan4.weekday()))
            monday_target = monday_week1 + timedelta(weeks=week_num - 1)
            week_dates = [monday_target + timedelta(days=j) for j in range(7)]

            # Título de la semana
            if len(semanas_a_exportar) > 1:
                week_title = f"Semana {week_num} ({week_dates[0].strftime('%d/%m')} - {week_dates[6].strftime('%d/%m/%Y')})"
                story.append(Paragraph(week_title, subtitle_style))
                story.append(Spacer(1, 10))

            # Obtener descansos de la semana
            fecha_inicio = week_dates[0]
            fecha_fin = week_dates[6]

            descansos = db.session.query(Descanso).join(Empleado).filter(
                Empleado.turno_id == turno_id,
                Empleado.activo == True,
                Descanso.fecha.between(fecha_inicio, fecha_fin)
            ).all()

            # Crear diccionario de descansos por empleado y fecha
            descansos_dict = {}
            for descanso in descansos:
                key = f"{descanso.empleado_id}_{descanso.fecha}"
                descansos_dict[key] = descanso.tipo

            # Crear tabla
            current_app.logger.info(f"📊 Creando tabla para semana {week_num}")
            data = []

            # Encabezados
            headers = ['Empleado', 'Sector']
            for fecha in week_dates:
                day_name = ['Lun', 'Mar', 'Mie', 'Jue', 'Vie', 'Sab', 'Dom'][fecha.weekday()]
                headers.append(f"{day_name} {fecha.strftime('%d/%m')}")

            data.append(headers)
            current_app.logger.info(f"📊 Encabezados: {len(headers)} columnas")

            # Datos de empleados
            for empleado in empleados:
                # Buscar sector asignado en descansos
                sector_asignado = None
                for descanso in descansos:
                    if descanso.empleado_id == empleado.id and hasattr(descanso, 'sector_id') and descanso.sector_id:
                        sector_obj = db.session.query(Sector).filter_by(id=descanso.sector_id).first()
                        if sector_obj:
                            sector_asignado = sector_obj.nombre
                            break

                if not sector_asignado:
                    sector_asignado = empleado.sector_rel.nombre if empleado.sector_rel else "N/A"

                row = [
                    f"{empleado.nombre} {empleado.apellidos}",
                    sector_asignado
                ]

                # Agregar estado para cada día
                for fecha in week_dates:
                    key = f"{empleado.id}_{fecha}"
                    estado = descansos_dict.get(key, "")

                    if estado == "DESCANSO":
                        row.append("D")
                    elif estado == "VACACIONES":
                        row.append("V")
                    elif estado == "BAJA":
                        row.append("B")
                    elif estado == "PERMISO":
                        row.append("P")
                    else:
                        row.append("")

                data.append(row)

            # Crear tabla con estilo simplificado
            current_app.logger.info(f"📊 Creando tabla con {len(data)} filas")
            try:
                table = Table(data)
                table.setStyle(TableStyle([
                    # Encabezados básicos
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 9),

                    # Datos básicos
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 1), (-1, -1), 8),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ]))

                story.append(table)
                current_app.logger.info("📊 Tabla agregada al documento")
            except Exception as e:
                current_app.logger.error(f"❌ Error creando tabla: {e}")
                # Agregar tabla simple como fallback
                simple_text = f"Error creando tabla para semana {week_num}: {str(e)}"
                story.append(Paragraph(simple_text, styles['Normal']))

            if i < len(semanas_a_exportar) - 1:  # No agregar espacio después de la última semana
                story.append(Spacer(1, 20))

        # Leyenda
        story.append(Spacer(1, 20))
        leyenda_text = "Leyenda: D = Descanso, V = Vacaciones, B = Baja, P = Permiso"
        story.append(Paragraph(leyenda_text, styles['Normal']))

        # Construir PDF
        doc.build(story)

        buffer.seek(0)
        return buffer

    except Exception as e:
        current_app.logger.error(f"Error generando PDF: {str(e)}")
        raise e