<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "urn:fontconfig:fonts.dtd">
<fontconfig>
  <description>Add sans-serif to the family when no generic name</description>
<!--
  If the font still has no generic name, add sans-serif
 -->
	<match target="pattern">
		<test qual="all" name="family" compare="not_eq">
			<string>sans-serif</string>
		</test>
		<test qual="all" name="family" compare="not_eq">
			<string>serif</string>
		</test>
		<test qual="all" name="family" compare="not_eq">
			<string>monospace</string>
		</test>
		<edit name="family" mode="append_last">
			<string>sans-serif</string>
		</edit>
	</match>
</fontconfig>
