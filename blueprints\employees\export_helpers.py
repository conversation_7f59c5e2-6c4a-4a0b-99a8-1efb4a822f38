from datetime import datetime, date
from sqlalchemy import and_, or_, not_
from models import Em<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, CalendarioLaboral, ConfiguracionDiaLegacy, Descanso
from database import db

def _filtrar_empleados_disponibles(query):
    """
    Filtra la consulta para incluir solo empleados disponibles (activos sin permisos activos).
    También considera los días de descanso del calendario laboral.
    
    Args:
        query: Consulta SQLAlchemy a filtrar
        
    Returns:
        Consulta SQLAlchemy filtrada
    """
    fecha_actual = date.today()
    
    # Subconsulta para obtener IDs de empleados con permisos activos
    subconsulta_permisos = db.session.query(Permiso.empleado_id).filter(
        Permiso.estado == 'Aprobado',
        Permiso.fecha_inicio <= fecha_actual,
        or_(
            Permiso.fecha_fin >= fecha_actual,
            Permiso.sin_fecha_fin == True
        )
    ).distinct().subquery()
    
    # Subconsulta para obtener empleados que tienen día de descanso hoy
    subconsulta_descanso = db.session.query(Descanso.empleado_id).filter(
        Descanso.fecha == fecha_actual,
        Descanso.tipo == 'DESCANSO'
    ).distinct().subquery()
    
    # Filtrar empleados activos que:
    # 1. NO están en la lista de permisos activos
    # 2. NO están en su día de descanso según el calendario
    return query.filter(
        Empleado.activo == True,
        ~Empleado.id.in_(subconsulta_permisos),
        ~Empleado.id.in_(subconsulta_descanso)
    )
