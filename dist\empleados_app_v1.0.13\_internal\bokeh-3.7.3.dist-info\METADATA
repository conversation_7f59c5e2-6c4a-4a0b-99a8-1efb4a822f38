Metadata-Version: 2.4
Name: bokeh
Version: 3.7.3
Summary: Interactive plots and applications in the browser from Python
Author: Bokeh Team
Author-email: <EMAIL>
License: Copyright (c) Anaconda, Inc., and Bokeh Contributors
        All rights reserved.
        
        Redistribution and use in source and binary forms, with or without modification,
        are permitted provided that the following conditions are met:
        
        Redistributions of source code must retain the above copyright notice,
        this list of conditions and the following disclaimer.
        
        Redistributions in binary form must reproduce the above copyright notice,
        this list of conditions and the following disclaimer in the documentation
        and/or other materials provided with the distribution.
        
        Neither the name of <PERSON><PERSON><PERSON> nor the names of any contributors
        may be used to endorse or promote products derived from this software
        without specific prior written permission.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
        AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
        IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
        ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
        LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
        CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
        SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
        INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
        CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
        ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
        THE POSSIBILITY OF SUCH DAMAGE.
        
Project-URL: homepage, https://bokeh.org
Project-URL: documentation, https://docs.bokeh.org
Project-URL: repository, https://github.com/bokeh/bokeh
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: End Users/Desktop
Classifier: Intended Audience :: Financial and Insurance Industry
Classifier: Intended Audience :: Healthcare Industry
Classifier: Intended Audience :: Information Technology
Classifier: Intended Audience :: Legal Industry
Classifier: Intended Audience :: Other Audience
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: JavaScript
Classifier: Topic :: Office/Business
Classifier: Topic :: Office/Business :: Financial
Classifier: Topic :: Scientific/Engineering
Classifier: Topic :: Scientific/Engineering :: Visualization
Classifier: Topic :: Scientific/Engineering :: Mathematics
Classifier: Topic :: Scientific/Engineering :: Information Analysis
Classifier: Topic :: Utilities
Requires-Python: >=3.10
Description-Content-Type: text/markdown
License-File: LICENSE.txt
Requires-Dist: Jinja2>=2.9
Requires-Dist: contourpy>=1.2
Requires-Dist: narwhals>=1.13
Requires-Dist: numpy>=1.16
Requires-Dist: packaging>=16.8
Requires-Dist: pandas>=1.2
Requires-Dist: pillow>=7.1.0
Requires-Dist: PyYAML>=3.10
Requires-Dist: tornado>=6.2; sys_platform != "emscripten"
Requires-Dist: xyzservices>=2021.09.1
Dynamic: license-file

<picture>
  <source media="(prefers-color-scheme: light)" srcset="https://raw.githubusercontent.com/bokeh/pm/main/assets/logos/SVG/bokeh-logo-white-text-no-padding.svg">
  <img src="https://raw.githubusercontent.com/bokeh/pm/main/assets/logos/SVG/bokeh-logo-black-text-no-padding.svg" alt="Bokeh logo -- text is white in dark theme and black in light theme" height=60/>
</picture>

----

[Bokeh](https://bokeh.org) is an interactive visualization library for modern web browsers. It provides elegant, concise construction of versatile graphics and affords high-performance interactivity across large or streaming datasets. Bokeh can help anyone who wants to create interactive plots, dashboards, and data applications quickly and easily.

<table>

<tr>

  <td>Package</td>

  <td>
    <img src="https://img.shields.io/pypi/v/bokeh?label=Version&color=ECD078&style=for-the-badge"
         alt="Latest package version" />
  </td>

  <td>
    <a href="https://docs.bokeh.org/en/latest/docs/first_steps/installation.html">
    <img src="https://img.shields.io/pypi/pyversions/bokeh?color=ECD078&style=for-the-badge"
         alt="Supported Python versions" />
    </a>
  </td>

  <td>
    <a href="https://github.com/bokeh/bokeh/blob/-/LICENSE.txt">
    <img src="https://img.shields.io/github/license/bokeh/bokeh.svg?color=ECD078&style=for-the-badge"
         alt="Bokeh license (BSD 3-clause)" />
    </a>
  </td>

</tr>

<tr>

  <td>Project</td>

  <td>
    <img src="https://img.shields.io/github/contributors-anon/bokeh/bokeh?color=ECD078&style=for-the-badge"
         alt="Github contributors" />
  </td>

  <td>
    <a href="https://numfocus.org">
    <img src="https://img.shields.io/badge/sponsor-numfocus-ECD078?style=for-the-badge"
         alt="Link to NumFOCUS" />
    </a>
  </td>

  <td>
    <a href="https://docs.bokeh.org/en/latest/">
    <img src="https://img.shields.io/badge/documentation-latest-ECD078?style=for-the-badge"
         alt="Link to documentation" />
    </a>
  </td>

</tr>

<tr>

  <td>Downloads</td>

  <td>
    <a href="https://docs.bokeh.org/en/latest/docs/first_steps/installation.html">
    <img src="https://img.shields.io/pypi/dm/bokeh?color=D98B43&label=pypi&logo=python&logoColor=yellow&style=for-the-badge"
         alt="PyPI downloads per month" />
    </a>
  </td>

  <td>
    <a href="https://docs.bokeh.org/en/latest/docs/first_steps/installation.html">
    <img src="https://img.shields.io/conda/d/conda-forge/bokeh?style=for-the-badge&logo=python&color=D98B43&logoColor=yellow"
         alt="Conda downloads per month" />
    </a>
  </td>

  <td>
    <a href="https://www.npmjs.com/package/@bokeh/bokehjs">
    <img src="https://img.shields.io/npm/dm/%40bokeh/bokehjs?style=for-the-badge&logo=npm&label=NPM&color=D98B43"
         alt="NPM downloads per month" />
    </a>
  </td>

</tr>

<tr>

  <td>Build</td>

  <td>
    <a href="https://github.com/bokeh/bokeh/actions">
    <img src="https://img.shields.io/github/actions/workflow/status/bokeh/bokeh/bokeh-ci.yml?label=Bokeh-CI&logo=github&style=for-the-badge"
         alt="Current Bokeh-CI github actions build status" />
    </a>
  </td>

  <td>
    <a href="https://github.com/bokeh/bokeh/actions">
    <img src="https://img.shields.io/github/actions/workflow/status/bokeh/bokeh/bokehjs-ci.yml?label=BokehJS-CI&logo=github&style=for-the-badge"
         alt="Current BokehJS-CI github actions build status" />
    </a>
  </td>

  <td>
    <a href="https://codecov.io/gh/bokeh/bokeh" >
    <img src="https://img.shields.io/codecov/c/github/bokeh/bokeh?logo=codecov&style=for-the-badge&token=bhEzGkDUaw"
         alt="Codecov coverage percentage" />
    </a>
  </td>

</tr>

<tr>

  <td>Community</td>

  <td>
    <a href="https://discourse.bokeh.org">
    <img src="https://img.shields.io/discourse/https/discourse.bokeh.org/posts.svg?color=blue&logo=discourse&style=for-the-badge"
         alt="Community support on discourse.bokeh.org" />
    </a>
  </td>

  <td>
    <a href="https://stackoverflow.com/questions/tagged/bokeh">
    <img src="https://img.shields.io/stackexchange/stackoverflow/t/%5Bbokeh%5D?style=for-the-badge&logo=stackoverflow&label=stackoverflow&color=blue"
         alt="Bokeh-tagged questions on Stack Overflow" />
     </a>
  </td>

</tr>


</table>

*Consider [making a donation](https://opencollective.com/bokeh) if you enjoy using Bokeh and want to support its development.*

![4x9 image grid of Bokeh plots](https://user-images.githubusercontent.com/1078448/190840954-dc243c99-9295-44de-88e9-fafd0f4f7f8a.jpg)

## Installation

To install Bokeh and its required dependencies using `pip`, enter the following command at a Bash or Windows command prompt:
```
pip install bokeh
```

To install using `conda`, enter the following command at a Bash or Windows command prompt:

```
conda install bokeh
```

Refer to the [installation documentation](https://docs.bokeh.org/en/latest/docs/first_steps/installation.html) for more details.

## Resources

Once Bokeh is installed, check out the [first steps guides](https://docs.bokeh.org/en/latest/docs/first_steps.html#first-steps-guides).

Visit the [full documentation site](https://docs.bokeh.org) to view the [User's Guide](https://docs.bokeh.org/en/latest/docs/user_guide.html) or [checkout the Bokeh tutorial repository](https://github.com/bokeh/tutorial/) to learn about Bokeh in live Jupyter Notebooks.

Community support is available on the [Project Discourse](https://discourse.bokeh.org).

If you would like to contribute to Bokeh, please review the [Contributor Guide](https://docs.bokeh.org/en/latest/docs/dev_guide.html) and [request an invitation to the Bokeh Dev Slack workspace](https://slack-invite.bokeh.org/).

*Note: Everyone who engages in the Bokeh project's discussion forums, codebases, and issue trackers is expected to follow the [Code of Conduct](https://github.com/bokeh/bokeh/blob/HEAD/docs/CODE_OF_CONDUCT.md).*

## Support

### Fiscal Support

The Bokeh project is grateful for [individual contributions](https://opencollective.com/bokeh), as well as for monetary support from the organizations and companies listed below:

<table align="center">
<tr>

  <td>
    <a href="https://www.numfocus.org/">
    <img src="https://static.bokeh.org/sponsor/numfocus.svg"
         alt="NumFocus Logo" width="200"/>
    </a>
  </td>

  <td>
    <a href="https://chanzuckerberg.com/">
    <img src="https://static.bokeh.org/sponsor/czi.svg"
         alt="CZI Logo" width="200"/>
    </a>
  </td>

  <td colspan="2">
    <a href="https://www.blackstone.com/the-firm/">
    <img src="https://static.bokeh.org/sponsor/blackstone.png"
         alt="Blackstone Logo" width="400"/>
    </a>
  </td>

 </tr>
 <tr>

  <td>
    <a href="https://tidelift.com/">
    <img src="https://static.bokeh.org/sponsor/tidelift.svg"
         alt="TideLift Logo" width="200"/>
    </a>
  </td>

  <td>
    <a href="https://www.anaconda.com/">
    <img src="https://static.bokeh.org/sponsor/anaconda.png"
         alt="Anaconda Logo" width="200"/>
    </a>
  </td>

  <td>
    <a href="https://www.nvidia.com">
    <img src="https://static.bokeh.org/sponsor/nvidia.png"
         alt="NVidia Logo" width="200"/>
    </a>
  </td>

  <td>
    <a href="https://developer.nvidia.com/rapids">
    <img src="https://static.bokeh.org/sponsor/rapids.png"
         alt="Rapids Logo" width="200"/>
    </a>
  </td>

</tr>
</table>

If your company uses Bokeh and is able to sponsor the project, please contact <a href="<EMAIL>"><EMAIL></a>

*Bokeh is a Sponsored Project of NumFOCUS, a 501(c)(3) nonprofit charity in the United States. NumFOCUS provides Bokeh with fiscal, legal, and administrative support to help ensure the health and sustainability of the project. Visit [numfocus.org](https://numfocus.org) for more information.*

*Donations to Bokeh are managed by NumFOCUS. For donors in the United States, your gift is tax-deductible to the extent provided by law. As with any donation, you should consult with your tax adviser about your particular tax situation.*

### In-kind Support

Non-monetary support can help with development, collaboration, infrastructure, security, and vulnerability management. The Bokeh project is grateful to the following companies for their donation of services:

* [Amazon Web Services](https://aws.amazon.com/)
* [GitGuardian](https://gitguardian.com/)
* [GitHub](https://github.com/)
* [makepath](https://makepath.com/)
* [Pingdom](https://www.pingdom.com/website-monitoring)
* [Slack](https://slack.com)
* [QuestionScout](https://www.questionscout.com/)
* [1Password](https://1password.com/)
