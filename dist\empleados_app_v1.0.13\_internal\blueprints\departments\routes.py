# -*- coding: utf-8 -*-
from flask import render_template, request, redirect, url_for, flash, jsonify
from . import departments_bp
from models import Departamento, Empleado, HistorialCambios, db
from datetime import datetime
from sqlalchemy.exc import IntegrityError

@departments_bp.route('/')
def list_departments():
    """Lista todos los departamentos"""
    departamentos = Departamento.query.order_by(Departamento.nombre).all()

    # Contar empleados por departamento
    for departamento in departamentos:
        departamento.num_empleados = Empleado.query.filter_by(
            departamento_id=departamento.id,
            activo=True
        ).count()

    return render_template('departments/list.html', departamentos=departamentos)

@departments_bp.route('/nuevo', methods=['GET', 'POST'])
def new_department():
    """Crea un nuevo departamento"""
    if request.method == 'POST':
        nombre = request.form.get('nombre', '').strip()

        if not nombre:
            flash('El nombre del departamento es obligatorio', 'error')
            return redirect(url_for('departments.new_department'))

        try:
            # Verificar si ya existe un departamento con ese nombre
            existing = Departamento.query.filter(
                db.func.lower(Departamento.nombre) == db.func.lower(nombre)
            ).first()

            if existing:
                flash(f'Ya existe un departamento con el nombre "{nombre}"', 'error')
                return redirect(url_for('departments.new_department'))

            # Crear nuevo departamento
            departamento = Departamento(nombre=nombre)
            db.session.add(departamento)

            # Registrar en historial
            historial = HistorialCambios(
                tipo_cambio='CREAR',
                entidad='Departamento',
                entidad_id=0,  # Se actualizará después del commit
                descripcion=f'Creación del departamento: {nombre}'
            )
            db.session.add(historial)

            db.session.flush()  # Para obtener el ID del departamento
            historial.entidad_id = departamento.id

            db.session.commit()
            flash(f'Departamento "{nombre}" creado correctamente', 'success')
            return redirect(url_for('departments.list_departments'))

        except Exception as e:
            db.session.rollback()
            flash(f'Error al crear el departamento: {str(e)}', 'error')
            return redirect(url_for('departments.new_department'))

    return render_template('departments/new.html')

@departments_bp.route('/editar/<int:id>', methods=['GET', 'POST'])
def edit_department(id):
    """Edita un departamento existente"""
    departamento = Departamento.query.get_or_404(id)

    if request.method == 'POST':
        nombre = request.form.get('nombre', '').strip()

        if not nombre:
            flash('El nombre del departamento es obligatorio', 'error')
            return redirect(url_for('departments.edit_department', id=id))

        try:
            # Verificar si ya existe otro departamento con ese nombre
            existing = Departamento.query.filter(
                db.func.lower(Departamento.nombre) == db.func.lower(nombre),
                Departamento.id != id
            ).first()

            if existing:
                flash(f'Ya existe otro departamento con el nombre "{nombre}"', 'error')
                return redirect(url_for('departments.edit_department', id=id))

            nombre_anterior = departamento.nombre
            departamento.nombre = nombre

            # Registrar en historial
            historial = HistorialCambios(
                tipo_cambio='EDITAR',
                entidad='Departamento',
                entidad_id=id,
                descripcion=f'Modificación del departamento: {nombre_anterior} → {nombre}'
            )
            db.session.add(historial)

            db.session.commit()
            flash(f'Departamento actualizado correctamente', 'success')
            return redirect(url_for('departments.list_departments'))

        except Exception as e:
            db.session.rollback()
            flash(f'Error al actualizar el departamento: {str(e)}', 'error')
            return redirect(url_for('departments.edit_department', id=id))

    return render_template('departments/edit.html', departamento=departamento)

@departments_bp.route('/eliminar/<int:id>', methods=['POST'])
def delete_department(id):
    """Elimina un departamento si no tiene empleados asociados"""
    departamento = Departamento.query.get_or_404(id)

    # Verificar si hay empleados asociados
    empleados_count = Empleado.query.filter_by(departamento_id=id).count()

    if empleados_count > 0:
        flash(f'No se puede eliminar el departamento "{departamento.nombre}" porque tiene {empleados_count} empleados asociados', 'error')
        return redirect(url_for('departments.list_departments'))

    try:
        nombre = departamento.nombre

        # Registrar en historial
        historial = HistorialCambios(
            tipo_cambio='ELIMINAR',
            entidad='Departamento',
            entidad_id=id,
            descripcion=f'Eliminación del departamento: {nombre}'
        )
        db.session.add(historial)

        # Eliminar departamento
        db.session.delete(departamento)
        db.session.commit()

        flash(f'Departamento "{nombre}" eliminado correctamente', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error al eliminar el departamento: {str(e)}', 'error')

    return redirect(url_for('departments.list_departments'))

@departments_bp.route('/check-name', methods=['POST'])
def check_department_name():
    """Verifica si un nombre de departamento ya existe (para validación AJAX)"""
    nombre = request.form.get('nombre', '').strip()
    departamento_id = request.form.get('id', 0, type=int)

    query = Departamento.query.filter(db.func.lower(Departamento.nombre) == db.func.lower(nombre))

    if departamento_id > 0:
        query = query.filter(Departamento.id != departamento_id)

    exists = query.first() is not None

    return jsonify({'exists': exists})
