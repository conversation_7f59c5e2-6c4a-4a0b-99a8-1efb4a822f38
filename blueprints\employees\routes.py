# -*- coding: utf-8 -*-
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

from .utils import get_contract_status, format_remaining_time
import io
import tempfile
import shutil
import json
import zipfile
from datetime import datetime, timedelta, date
from pathlib import Path
from functools import wraps
from dateutil.relativedelta import relativedelta
import logging
import os

# Third-party imports
from flask import (
    Blueprint, render_template, request, redirect, url_for, flash, current_app,
    send_from_directory, make_response, jsonify, abort, session, send_file, Response
)
from flask_login import login_required, current_user
from sqlalchemy import func, case, or_, and_, text, create_engine, extract
from sqlalchemy.orm import aliased, joinedload
from sqlalchemy.sql import label
from sqlalchemy.exc import SQLAlchemyError
import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
from reportlab.lib.pagesizes import letter, landscape, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from xlsxwriter.workbook import Workbook
from werkzeug.utils import secure_filename
from dateutil.relativedelta import relativedelta
import numpy as np

# Local application imports
from . import employees_bp
from models import (
    db, Empleado, Sector, Departamento, Permiso, HistorialCambios, Usuario,
    TipoPermiso, Evaluacion, Turno,
    CalendarioLaboral, ConfiguracionDiaLegacy, ExcepcionTurno, CLASIFICACION_EVALUACION,
    TIPOS_CONTRATO, CARGOS
)
from services.employee_service import EmployeeService
from models_polivalencia import Polivalencia, NIVELES_POLIVALENCIA
from .export_helpers import _filtrar_empleados_disponibles
from services.audit_service import AuditService

def _filtrar_bajas_medicas_activas(query):
    """
    Filtra la consulta para incluir solo empleados con bajas médicas activas.
    
    Args:
        query: Consulta SQLAlchemy a filtrar
        
    Returns:
        Consulta SQLAlchemy filtrada
    """
    hoy = datetime.now().date()
    
    # Subconsulta para obtener empleados con bajas médicas activas
    subquery = db.session.query(Permiso.empleado_id).filter(
        Permiso.tipo_permiso == 'Baja Médica',
        Permiso.estado == 'Aprobado',
        Permiso.fecha_inicio <= hoy,
        db.or_(
            Permiso.fecha_fin >= hoy,
            Permiso.sin_fecha_fin == True
        )
    ).distinct().subquery()
    
    # Filtrar solo empleados que tengan bajas médicas activas
    return query.filter(Empleado.id.in_(subquery))

# Configuración simple del logger
import tempfile

# Crear un logger específico para esta aplicación
logger = logging.getLogger('empleados_app')
logger.setLevel(logging.DEBUG)

# Crear un manejador de archivo
log_file = os.path.join(tempfile.gettempdir(), 'empleados_debug_simple.log')

# Configurar el manejador de archivo
file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
file_handler.setLevel(logging.DEBUG)

# Crear un formateador
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)

# Limpiar manejadores existentes
if logger.hasHandlers():
    logger.handlers.clear()

# Añadir el manejador al logger
logger.addHandler(file_handler)

# Configurar también el logging básico
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[file_handler]
)

# Mensaje de prueba
logger.debug('=' * 60)
logger.debug('INICIO DE LA APLICACIÓN - LOGGER SIMPLIFICADO')
logger.debug('=' * 60)
logger.debug(f'Archivo de log: {log_file}')
logger.debug(f'Directorio de trabajo: {os.getcwd()}')

# Función para registrar información del sistema
try:
    import platform
    logger.debug(f'Sistema operativo: {platform.system()} {platform.release()}')
    logger.debug(f'Python: {platform.python_version()}')
    logger.debug(f'Directorio de trabajo: {os.getcwd()}')
    logger.debug(f'Ruta del script: {os.path.abspath(__file__)}')
    logger.debug('Prueba de escritura en el archivo de log')
    
    # Verificar si podemos escribir en el archivo
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write('Prueba de escritura directa en el archivo de log\n')
    
    logger.debug('Escritura de prueba completada con éxito')
    
except Exception as e:
    # Si hay un error, intentar registrar en un archivo de error
    error_file = os.path.join(tempfile.gettempdir(), 'empleados_error.log')
    try:
        with open(error_file, 'a', encoding='utf-8') as f:
            f.write(f'[{datetime.now()}] Error: {str(e)}\n')
            f.write(traceback.format_exc() + '\n')
    except:
        pass  # No podemos hacer nada más si falla esto

employee_service = EmployeeService()

@employees_bp.route('/')
def list_employees():
    # Obtener filtros de la request
    filtros_aplicados = {k: v for k, v in request.args.items() if k not in ['pagina', 'per_page', 'ordenar'] and v}
    pagina = int(request.args.get('pagina', 1))
    # Solo reiniciar a página 1 si hay filtros nuevos (por ejemplo, si se hace un POST o cambia algún filtro)
    if request.method == 'POST' or (len(filtros_aplicados) > 0 and 'pagina' not in request.args):
        pagina = 1
    empleados_por_pagina = request.args.get('per_page', 0, type=int)  # 0 significa sin límite por defecto
    
    # Si hay filtros, usar paginación
    if len(filtros_aplicados) > 0 and empleados_por_pagina == 0:
        empleados_por_pagina = 10
    
    ordenar = request.args.get('ordenar', 'ficha')
    
    # Obtener y validar fechas de ingreso
    fecha_ingreso_desde = request.args.get('fecha_ingreso_desde', '')
    fecha_ingreso_hasta = request.args.get('fecha_ingreso_hasta', '')
    
    # Obtener fechas de disponibilidad
    fecha_desde = request.args.get('fecha_desde', '')
    fecha_hasta = request.args.get('fecha_hasta', '')
    
    # Validar formato de fechas
    try:
        if fecha_ingreso_desde:
            datetime.strptime(fecha_ingreso_desde, '%Y-%m-%d')
        else:
            fecha_ingreso_desde = None
            
        if fecha_ingreso_hasta:
            datetime.strptime(fecha_ingreso_hasta, '%Y-%m-%d')
        else:
            fecha_ingreso_hasta = None
            
        if fecha_desde:
            datetime.strptime(fecha_desde, '%Y-%m-%d')
        else:
            fecha_desde = None
            
        if fecha_hasta:
            datetime.strptime(fecha_hasta, '%Y-%m-%d')
        else:
            fecha_hasta = None
            
        # Validar que fecha_desde no sea mayor que fecha_hasta
        if fecha_desde and fecha_hasta and fecha_desde > fecha_hasta:
            fecha_desde, fecha_hasta = fecha_hasta, fecha_desde
            flash('Se han intercambiado las fechas de disponibilidad para que sean coherentes', 'info')
            
    except ValueError:
        flash('Formato de fecha inválido. Use el formato AAAA-MM-DD', 'error')
        fecha_ingreso_desde = fecha_ingreso_hasta = fecha_desde = fecha_hasta = None
    
    # Obtener y validar antigüedad
    antiguedad_min = request.args.get('antiguedad_min', type=int)
    antiguedad_max = request.args.get('antiguedad_max', type=int)
    
    # Asegurar que la antigüedad mínima no sea mayor que la máxima
    if antiguedad_min is not None and antiguedad_max is not None and antiguedad_min > antiguedad_max:
        antiguedad_min, antiguedad_max = antiguedad_max, antiguedad_min
        flash('Se han intercambiado los valores de antigüedad mínima y máxima para que sean coherentes', 'info')
    
    # Obtener todos los parámetros de filtrado
    filtros = {
        'busqueda': request.args.get('busqueda', '').strip() or None,
        'departamento': request.args.get('departamento', '').strip() or None,
        'cargo': request.args.get('cargo', '').strip() or None,
        'estado': request.args.get('estado', '').strip().lower() if request.args.get('estado') else 'activo',
        'turno': request.args.get('turno', '').strip() or None,
        'sector': request.args.get('sector', '').strip() or None,
        'tipo_contrato': request.args.get('tipo_contrato', '').strip() or None,
        'fecha_ingreso_desde': fecha_ingreso_desde,
        'fecha_ingreso_hasta': fecha_ingreso_hasta,
        'fecha_desde': fecha_desde,
        'fecha_hasta': fecha_hasta,
        'antiguedad_min': antiguedad_min,
        'antiguedad_max': antiguedad_max,
        'solo_disponibles': request.args.get('solo_disponibles', '') == '1',
        'solo_bajas_medicas': request.args.get('solo_bajas_medicas', '') == '1',
        'festivos_manana_disponibles': request.args.get('festivos_manana_disponibles', '') == '1',
        'activos_disponibles': request.args.get('activos_disponibles', '') == '1',
        'excluir_encargados': request.args.get('excluir_encargados', '') == '1'
    }

    # Obtener empleados filtrados y paginados usando el servicio específico
    resultado = employee_service.get_employees_list(
        pagina=pagina,
        por_pagina=empleados_por_pagina,
        ordenar=ordenar,
        **filtros
    )

    # Obtener listas para los filtros
    departamentos = db.session.query(Departamento.nombre).distinct().order_by(Departamento.nombre).all()
    cargos = db.session.query(Empleado.cargo).distinct().order_by(Empleado.cargo).all()
    sectores = Sector.query.order_by(Sector.nombre).all()
    departamentos_obj = Departamento.query.order_by(Departamento.nombre).all()
    turnos = ['Mañana', 'Tarde', 'Noche', 'Festivos Mañana', 'Festivos Noche']
    tipos_contrato = ['Plantilla Empresa', 'ETT']

    # Preparar datos para la plantilla
    context = {
        'empleados': resultado['empleados'],
        'pagina': resultado['pagina_actual'],
        'per_page': empleados_por_pagina,
        'total_paginas': resultado['total_paginas'],
        'total_empleados': resultado['total_empleados'],
        'ordenar': ordenar,
        'filtros_activos': {k: v for k, v in filtros.items() if v and v != '' and v is not None and v is not False},
        'filtro_departamento': filtros['departamento'],
        'filtro_cargo': filtros['cargo'],
        'filtro_estado': filtros['estado'],
        'filtro_turno': filtros['turno'],
        'filtro_sector': filtros['sector'],
        'filtro_tipo_contrato': filtros['tipo_contrato'],
        'filtro_busqueda': filtros['busqueda'],
        'departamentos': [d[0] for d in departamentos if d[0]],
        'cargos': [c[0] for c in cargos if c[0]],
        'sectores': sectores,
        'departamentos_obj': departamentos_obj,
        'turnos': turnos,
        'tipos_contrato': tipos_contrato,
        'solo_disponibles': filtros['solo_disponibles'],
        'solo_bajas_medicas': filtros['solo_bajas_medicas'],
        'festivos_manana_disponibles': filtros['festivos_manana_disponibles'],
        'activos_disponibles': filtros['activos_disponibles']
    }

    return render_template('empleados.html', **context)

@employees_bp.route('/detalle/<int:id>')
def employee_detail(id):
    # Obtener el empleado por ID
    empleado = Empleado.query.options(
        joinedload(Empleado.sector_rel),
        joinedload(Empleado.departamento_rel)
    ).get_or_404(id)

    # Obtener las polivalencias del empleado si el módulo está disponible
    try:
        polivalencias = Polivalencia.query.filter_by(empleado_id=empleado.id).all()
        niveles = NIVELES_POLIVALENCIA
    except:
        polivalencias = []
        niveles = {}

    # Obtener los permisos del empleado
    permisos_pendientes = Permiso.query.filter_by(
        empleado_id=empleado.id,
        estado='Pendiente'
    ).order_by(Permiso.fecha_inicio.desc()).all()

    # --- MODIFICADO: Obtener todos los permisos aprobados para el resumen ---
    permisos_aprobados_todos = Permiso.query.filter_by(
        empleado_id=empleado.id,
        estado='Aprobado'
    ).order_by(Permiso.fecha_inicio.desc()).all()

    # --- NUEVO: Calcular días acumulados por tipo de permiso ---
    from collections import defaultdict
    resumen_dias_permisos = defaultdict(int)
    fecha_actual_calculo = datetime.now().date()
    for permiso in permisos_aprobados_todos:
        dias = permiso.calcular_dias(fecha_actual_calculo)
        resumen_dias_permisos[permiso.tipo_permiso] += dias

    permisos_aprobados = permisos_aprobados_todos[:5] # Limitar para la vista de recientes

    # Obtener la fecha actual para comparar con las fechas de renovación
    import csv
    import io
    import os
    import tempfile
    import pandas as pd
    from reportlab.lib.pagesizes import letter
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
    from reportlab.lib import colors
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from io import BytesIO
    from xlsxwriter.workbook import Workbook
    now = datetime.now()

    # Obtener las últimas 4 anotaciones del empleado
    from models import Anotacion
    ultimas_anotaciones = Anotacion.query.filter_by(empleado_id=empleado.id).order_by(Anotacion.fecha_creacion.desc()).limit(4).all()

    # Obtener todos los permisos del empleado (aprobados y pendientes)
    todos_permisos = Permiso.query.filter_by(empleado_id=empleado.id).order_by(Permiso.fecha_inicio.desc()).all()

    return render_template('empleado_detalle.html',
                         empleado=empleado,
                         polivalencias=polivalencias,
                         niveles=niveles,
                         permisos_pendientes=permisos_pendientes,
                         permisos_aprobados=permisos_aprobados,
                         resumen_dias_permisos=dict(resumen_dias_permisos),
                         clasificaciones=CLASIFICACION_EVALUACION,
                         now=now,
                         ultimas_anotaciones=ultimas_anotaciones,
                         todos_permisos=todos_permisos
    )

@employees_bp.route('/nuevo', methods=['GET', 'POST'])
def new_employee():
    # Obtener todos los sectores, departamentos y turnos para el formulario
    sectores = Sector.query.order_by(Sector.nombre).all()
    departamentos = Departamento.query.order_by(Departamento.nombre).all()
    turnos = Turno.query.order_by(Turno.tipo).all() # Obtener turnos de la base de datos

    form_data = request.form
    if request.method == 'POST' and form_data:
        try:
            # Crear un nuevo empleado con los datos del formulario (solo usar campos que existen en el modelo)
            nuevo_empleado = Empleado(
                ficha=request.form['ficha'],
                nombre=request.form['nombre'],
                apellidos=request.form['apellidos'],
                fecha_ingreso=datetime.strptime(request.form['fecha_ingreso'], '%Y-%m-%d').date() if request.form.get('fecha_ingreso') else datetime.now().date(),
                fecha_finalizacion=datetime.strptime(request.form['fecha_finalizacion'], '%Y-%m-%d').date() if request.form.get('fecha_finalizacion') else None,
                fecha_nacimiento=datetime.strptime(request.form['fecha_nacimiento'], '%Y-%m-%d').date() if request.form.get('fecha_nacimiento') else None,
                sexo=request.form.get('sexo', ''),
                cargo=request.form.get('cargo', ''),
                departamento_id=int(request.form.get('departamento_id')) if request.form.get('departamento_id') else None,
                sector_id=int(request.form.get('sector_id')) if request.form.get('sector_id') else None,
                turno=request.form.get('turno', ''),
                tipo_contrato=request.form.get('tipo_contrato', ''),
                activo=True if request.form.get('activo') == '1' else False,
                observaciones=request.form.get('observaciones', ''),
                # Información adicional
                dni=request.form.get('dni', ''),
                email=request.form.get('email', ''),
                telefono=request.form.get('telefono', ''),
                direccion=request.form.get('direccion', '')
            )

            # Guardar el nuevo empleado en la base de datos
            db.session.add(nuevo_empleado)
            db.session.commit()

            flash(f'Empleado {nuevo_empleado.nombre} {nuevo_empleado.apellidos} creado correctamente', 'success')
            return redirect(url_for('employees.employee_detail', id=nuevo_empleado.id))

        except Exception as e:
            db.session.rollback()
            flash(f'Error al crear el empleado: {str(e)}', 'error')

    # Obtener turnos de la base de datos
    turnos = Turno.query.order_by(Turno.id).all()
    current_app.logger.info(f"Turnos obtenidos de la base de datos: {[t.tipo for t in turnos]}")
    tipos_contrato = ['Plantilla Empresa', 'ETT']
    cargos = ['Encargado', 'Ayudante Encargado', 'Técnico', 'Operario']

    # Renderizar el formulario para crear un nuevo empleado
    return render_template('empleado_form.html',
                         modo='nuevo',
                         titulo='Nuevo Empleado',
                         subtitulo='Crear un nuevo registro de empleado',
                         sectores=sectores,
                         departamentos=departamentos,
                         turnos=turnos,
                         tipos_contrato=tipos_contrato,
                         cargos=cargos)

@employees_bp.route('/editar/<int:id>', methods=['GET', 'POST'])
def edit_employee(id):
    # Obtener el empleado por ID
    empleado = Empleado.query.get_or_404(id)

    # Obtener todos los sectores y departamentos para el formulario
    sectores = Sector.query.order_by(Sector.nombre).all()
    departamentos = Departamento.query.order_by(Departamento.nombre).all()

    if request.method == 'POST':
        try:
            # Actualizar los datos del empleado (solo campos que existen en el modelo)
            empleado.nombre = request.form['nombre']
            empleado.apellidos = request.form['apellidos']
            empleado.fecha_ingreso = datetime.strptime(request.form['fecha_ingreso'], '%Y-%m-%d').date() if request.form.get('fecha_ingreso') else empleado.fecha_ingreso
            empleado.fecha_finalizacion = datetime.strptime(request.form['fecha_finalizacion'], '%Y-%m-%d').date() if request.form.get('fecha_finalizacion') else None
            empleado.fecha_nacimiento = datetime.strptime(request.form['fecha_nacimiento'], '%Y-%m-%d').date() if request.form.get('fecha_nacimiento') else None
            empleado.sexo = request.form.get('sexo', '')
            empleado.cargo = request.form.get('cargo', '')
            empleado.departamento_id = int(request.form.get('departamento_id')) if request.form.get('departamento_id') else empleado.departamento_id
            empleado.sector_id = int(request.form.get('sector_id')) if request.form.get('sector_id') else empleado.sector_id
            # Actualizar el turno a través de la relación turno_rel
            turno_id = request.form.get('turno_id')
            if turno_id:
                turno = Turno.query.get(int(turno_id))
                if turno:
                    empleado.turno_rel = turno
            empleado.tipo_contrato = request.form.get('tipo_contrato', '')
            # Manejar el estado activo de forma más robusta
            activo_anterior = empleado.activo
            empleado.activo = request.form.get('activo') == '1'
            
            # Si el estado activo cambió, registrar el cambio
            if activo_anterior != empleado.activo:
                estado = 'activado' if empleado.activo else 'desactivado'
                current_app.logger.info(f'Estado de empleado ID {empleado.id} cambiado a: {estado}')
            empleado.observaciones = request.form.get('observaciones', '')

            # Actualizar información adicional
            empleado.dni = request.form.get('dni', '')
            empleado.email = request.form.get('email', '')
            empleado.telefono = request.form.get('telefono', '')
            empleado.direccion = request.form.get('direccion', '')

            # Guardar los cambios en la base de datos
            db.session.commit()
            
            # Forzar la actualización de la sesión
            db.session.refresh(empleado)
            
            # Limpiar la caché para asegurar que se vean los cambios
            if 'cache' in current_app.extensions:
                current_app.cache.clear()
                current_app.logger.info(f'Caché limpiada después de actualizar empleado ID {empleado.id}')
            
            current_app.logger.info(f'Empleado actualizado - ID: {empleado.id}, Nombre: {empleado.nombre} {empleado.apellidos}, Activo: {empleado.activo}')
            flash(f'Empleado {empleado.nombre} {empleado.apellidos} actualizado correctamente', 'success')
            return redirect(url_for('employees.employee_detail', id=empleado.id))

        except Exception as e:
            db.session.rollback()
            flash(f'Error al actualizar el empleado: {str(e)}', 'error')

    # Obtener turnos de la base de datos
    turnos = Turno.query.order_by(Turno.id).all()
    current_app.logger.info(f"Turnos obtenidos de la base de datos: {[t.tipo for t in turnos]}")
    tipos_contrato = ['Plantilla Empresa', 'ETT']
    cargos = ['Encargado', 'Ayudante Encargado', 'Técnico', 'Operario']

    # Renderizar el formulario para editar el empleado
    return render_template('empleado_form.html',
                         modo='editar',
                         titulo='Editar Empleado',
                         subtitulo='Modificar información del empleado',
                         empleado=empleado,
                         sectores=sectores,
                         departamentos=departamentos,
                         turnos=turnos,
                         tipos_contrato=tipos_contrato,
                         cargos=cargos)

@employees_bp.route('/eliminar/<int:id>', methods=['POST'])
def delete_employee(id):
    """Elimina un empleado y registra la acción en el historial"""
    try:
        empleado = Empleado.query.get_or_404(id)

        # Registrar eliminación en el historial usando el servicio de auditoría
        AuditService.registrar_cambio_empleado(
            empleado=empleado,
            tipo_cambio='ELIMINAR',
            usuario=current_user
        )

        # Eliminar el empleado
        db.session.delete(empleado)
        db.session.commit()
        flash('Empleado eliminado correctamente', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error al eliminar empleado: {str(e)}', 'error')

    return redirect(url_for('employees.list_employees'))

@employees_bp.route('/historial/<int:id>')
def employee_history(id):
    """Ver el historial de un empleado específico"""
    empleado = Empleado.query.get_or_404(id)

    # Obtener historial del empleado
    historial = HistorialCambios.query.filter_by(
        entidad='Empleado',
        entidad_id=empleado.id
    ).order_by(HistorialCambios.fecha.desc()).all()

    # Obtener permisos del empleado
    permisos = Permiso.query.filter_by(
        empleado_id=empleado.id
    ).order_by(Permiso.fecha_inicio.desc()).all()

    # Obtener evaluaciones del empleado
    evaluaciones = EvaluacionDetallada.query.filter_by(
        empleado_id=empleado.id
    ).order_by(EvaluacionDetallada.fecha_evaluacion.desc()).all()

    return render_template('historial_empleado.html',
                         empleado=empleado,
                         historial=historial,
                         permisos=permisos,
                         evaluaciones=evaluaciones,
                         title=f"Historial de {empleado.nombre} {empleado.apellidos}")


@employees_bp.route('/ett')
def list_ett_employees():
    """Lista de empleados ETT con filtros de contrato"""
    # Import datetime at function level to ensure it's available
    from datetime import datetime, timedelta
    from app import db
    from models import Sector, Departamento  # Asegúrate de importar los modelos
    
    # Obtener la fecha actual al inicio de la función
    today = datetime.now().date()
    
    # Obtener parámetros de filtro
    show_expiring = request.args.get('expiring') == '1'
    months_ahead = int(request.args.get('months_ahead', 6))
    filtro_turno = request.args.get('filtro_turno', '')  # Nuevo filtro por turno
    
    # Obtener todos los sectores y departamentos
    sectores = Sector.query.all()
    departamentos = Departamento.query.all()
    
    # Obtener todos los turnos únicos para el filtro
    turnos = db.session.query(Turno.tipo).distinct().all()
    turnos = [t[0] for t in turnos if t[0]]  # Convertir de lista de tuplas a lista de strings
    
    # Crear diccionarios para búsqueda rápida
    sectores_dict = {sector.id: sector for sector in sectores}
    departamentos_dict = {depto.id: depto for depto in departamentos}
    
    # Consulta base para empleados ETT activos con relaciones a Sector, Departamento y Turno
    query = db.session.query(
        Empleado,
        Sector.nombre.label('sector_nombre'),
        Departamento.nombre.label('departamento_nombre'),
        Turno  # Cambiado de Turno.tipo.label('turno_tipo') a Turno completo
    ).outerjoin(
        Sector, Sector.id == Empleado.sector_id
    ).outerjoin(
        Departamento, Departamento.id == Empleado.departamento_id
    ).outerjoin(
        Turno, Turno.id == Empleado.turno_id
    ).filter(
        Empleado.tipo_contrato == 'ETT',
        Empleado.activo == True
    )
    
    # Aplicar filtro de contratos próximos a vencer si está activo
    if show_expiring:
        end_date = today + relativedelta(months=months_ahead)
        
        # Filtrar por contratos que finalizan en el rango de fechas
        query = query.filter(
            Empleado.fecha_finalizacion.between(today, end_date)
        ).order_by(Empleado.fecha_finalizacion.asc())
    else:
        query = query.order_by(Empleado.apellidos, Empleado.nombre)
    
    empleados_raw = query.all()
    empleados = []

    for empleado, sector_nombre, departamento_nombre, current_turno in empleados_raw:
        days, status, formatted_date, remaining_time_formatted, _ = get_contract_status(
            empleado.fecha_finalizacion, 
            empleado.fecha_ingreso
        )
        
        empleados.append({
            'id': empleado.id,
            'nombre': empleado.nombre,
            'apellidos': empleado.apellidos,
            'ficha': empleado.ficha,
            'turno': empleado.turno,
            'sector_nombre': sector_nombre or 'N/A',
            'departamento_nombre': departamento_nombre or 'N/A',
            'activo': empleado.activo,
            'dias_restantes': days,
            'estado_contrato': status,
            'fecha_finalizacion_formateada': formatted_date,
            'tiempo_restante_formateado': remaining_time_formatted
        })

    # Filtrar y ordenar para las listas específicas
    empleados_6_meses = sorted([e for e in empleados if e['dias_restantes'] is not None and 0 <= e['dias_restantes'] <= 180], key=lambda x: x['dias_restantes'])
    empleados_1_anio = sorted([e for e in empleados if e['dias_restantes'] is not None and 0 <= e['dias_restantes'] <= 365], key=lambda x: x['dias_restantes'])
    
    # Calcular rotación por sector
    rotacion_por_sector = {}
    for sector in sectores:
        # Contar empleados activos por sector
        total_empleados = Empleado.query.filter_by(
            sector_id=sector.id,
            activo=True,
            tipo_contrato='ETT'
        ).count()
        
        # Contar bajas recientes (últimos 60 días)
        bajas_recientes = Empleado.query.filter(
            Empleado.sector_id == sector.id,
            Empleado.activo == False,
            Empleado.tipo_contrato == 'ETT',
            Empleado.fecha_finalizacion >= (today - timedelta(days=60))
        ).count()
        
        # Contar contratos por vencer (próximos 60 días)
        por_vencer = Empleado.query.filter(
            Empleado.sector_id == sector.id,
            Empleado.activo == True,
            Empleado.tipo_contrato == 'ETT',
            Empleado.fecha_finalizacion.between(today, today + timedelta(days=60))
        ).count()
        
        # Calcular tasa de rotación (bajas / promedio de empleados) * 100
        rotacion = 0
        if total_empleados > 0:
            rotacion = (bajas_recientes / total_empleados) * 100
        
        rotacion_por_sector[sector.id] = {
            'nombre': sector.nombre,
            'total_empleados': total_empleados,
            'bajas': bajas_recientes,
            'por_vencer': por_vencer,
            'rotacion': rotacion
        }
    
    # Calcular rotación por turno para los últimos 12 meses
    rotacion_turno_12_meses = {}
    turnos_activos = db.session.query(Turno).all()

    for i in range(12):
        fecha_inicio = (today.replace(day=1) - relativedelta(months=i))
        fecha_fin = (fecha_inicio + relativedelta(months=1)) - timedelta(days=1)
        mes_label = fecha_inicio.strftime('%B %Y').capitalize()

        rotacion_por_turno_mes = {}
        for turno in turnos_activos:
            if filtro_turno and turno.tipo != filtro_turno:
                continue  # Saltar turnos que no coinciden con el filtro
            total_empleados_turno = Empleado.query.filter(
                Empleado.tipo_contrato == 'ETT',
                Empleado.turno_id == turno.id,
                Empleado.activo == True
            ).count()

            bajas_turno_mes = Empleado.query.filter(
                Empleado.tipo_contrato == 'ETT',
                Empleado.turno_id == turno.id,
                Empleado.fecha_finalizacion.between(fecha_inicio, fecha_fin)
            ).count()

            altas_turno_mes = Empleado.query.filter(
                Empleado.tipo_contrato == 'ETT',
                Empleado.turno_id == turno.id,
                Empleado.fecha_ingreso.between(fecha_inicio, fecha_fin)
            ).count()

            if altas_turno_mes > 0 or bajas_turno_mes > 0:
                tasa_rotacion = (bajas_turno_mes / total_empleados_turno * 100) if total_empleados_turno > 0 else 0
                rotacion_por_turno_mes[turno.tipo] = {
                    'altas': altas_turno_mes,
                    'bajas': bajas_turno_mes,
                    'rotacion': round(tasa_rotacion, 2)
                }
        if rotacion_por_turno_mes:
            rotacion_turno_12_meses[mes_label] = rotacion_por_turno_mes
    
    # Generar datos para el gráfico de evolución (12 meses)
    meses_grafico = []
    altas_grafico = []
    bajas_grafico = []
    
    for i in range(11, -1, -1):
        fecha_inicio = (today.replace(day=1) - relativedelta(months=i))
        fecha_fin = (fecha_inicio + relativedelta(months=1)) - timedelta(days=1)
        
        altas_mes = db.session.query(Empleado.id).filter(
            Empleado.tipo_contrato == 'ETT',
            Empleado.fecha_ingreso.between(fecha_inicio, fecha_fin)
        ).count()
        
        bajas_mes = db.session.query(Empleado.id).filter(
            Empleado.tipo_contrato == 'ETT',
            Empleado.fecha_finalizacion.between(fecha_inicio, fecha_fin)
        ).count()
        
        if altas_mes > 0 or bajas_mes > 0:
            meses_grafico.append(fecha_inicio.strftime('%b %Y'))
            altas_grafico.append(altas_mes)
            bajas_grafico.append(bajas_mes)

    # Calcular líneas de tendencia
    tendencia_altas = []
    tendencia_bajas = []
    if len(meses_grafico) > 1:
        x = np.arange(len(meses_grafico))
        
        # Tendencia para altas
        y_altas = np.array(altas_grafico)
        coeffs_altas = np.polyfit(x, y_altas, 1)
        tendencia_altas = [coeffs_altas[0] * i + coeffs_altas[1] for i in x]
        
        # Tendencia para bajas
        y_bajas = np.array(bajas_grafico)
        coeffs_bajas = np.polyfit(x, y_bajas, 1)
        tendencia_bajas = [coeffs_bajas[0] * i + coeffs_bajas[1] for i in x]

    chart_data = {
        'labels': meses_grafico,
        'altas': altas_grafico,
        'bajas': bajas_grafico,
        'tendencia_altas': tendencia_altas,
        'tendencia_bajas': tendencia_bajas
    }
    
    return render_template(
        'empleados_ett.html',
        empleados=empleados,
        empleados_6_meses=empleados_6_meses,
        empleados_1_anio=empleados_1_anio,
        total_empleados=len(empleados),
        rotacion_mensual=0,  # Rotación mensual no se calcula aquí
        show_expiring=show_expiring,
        months_ahead=months_ahead,
        sectores=sectores,
        turnos=turnos,
        rotacion_por_sector=rotacion_por_sector,
        rotacion_turno_12_meses=rotacion_turno_12_meses,
        departamentos_dict=departamentos_dict,
        hoy=today,
        chart_data=chart_data
    )

@employees_bp.route('/importar', methods=['GET', 'POST'])
def import_employees():
    """Importar empleados desde un archivo Excel"""
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No se seleccionó ningún archivo', 'error')
            return redirect(request.url)

        file = request.files['file']
        if file.filename == '':
            flash('No se seleccionó ningún archivo', 'error')
            return redirect(request.url)

        if not file.filename.endswith(('.xlsx', '.xls')):
            flash('El archivo debe ser un Excel (.xlsx o .xls)', 'error')
            return redirect(request.url)

        try:
            # Usar el servicio para importar empleados
            result = employee_service.import_employees_from_excel(file)

            # Mostrar mensaje de resultado
            processed = result['processed']
            duplicates = result['duplicates']
            errors = result['errors']

            if processed > 0:
                mensaje = f'Se importaron {processed} registros correctamente.'
                if duplicates > 0:
                    mensaje += f' Se omitieron {duplicates} registros duplicados.'
                if errors > 0:
                    mensaje += f' Hubo {errors} errores.'
                    for error in result['error_messages']:
                        logging.error(error)
                flash(mensaje, 'success' if errors == 0 else 'warning')
            else:
                if duplicates > 0:
                    flash(f'No se importaron registros nuevos. {duplicates} registros estaban duplicados.', 'warning')
                else:
                    flash('No se pudo importar ningún registro', 'error')

            return redirect(url_for('employees.list_employees'))

        except Exception as e:
            flash(f'Error al procesar el archivo: {str(e)}', 'error')
            logging.error(f"Error al importar archivo: {str(e)}")
            return redirect(request.url)

    return render_template('importar.html')

@employees_bp.route('/exportar-csv')
def export_employees_csv():
    """Exportar empleados a CSV"""
    try:
        # Obtener los parámetros de filtro de la solicitud
        filtros = {
            'departamento': request.args.get('departamento', ''),
            'cargo': request.args.get('cargo', ''),
            'estado': request.args.get('estado', ''),
            'turno': request.args.get('turno', ''),
            'busqueda': request.args.get('busqueda', ''),
            'excluir_encargados': request.args.get('excluir_encargados', '') == 'on',
            'solo_bajas_medicas': request.args.get('solo_bajas_medicas', '') == 'on',
            'solo_disponibles': request.args.get('solo_disponibles', '') == 'on',
            'fecha_ingreso_desde': request.args.get('fecha_ingreso_desde', ''),
            'fecha_ingreso_hasta': request.args.get('fecha_ingreso_hasta', ''),
            'antiguedad_min': request.args.get('antiguedad_min', type=int),
            'antiguedad_max': request.args.get('antiguedad_max', type=int)
        }
        
        # Obtener empleados filtrados usando el servicio
        resultado = employee_service.get_filtered_employees(**filtros)
        empleados = resultado['empleados']
        
        # Crear el archivo CSV en memoria
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Escribir encabezados
        writer.writerow([
            'Ficha', 'Nombre', 'Apellidos', 'Departamento', 'Cargo', 
            'Turno', 'Estado', 'Fecha Ingreso', 'Sexo', 'Observaciones'
        ])
        
        # Escribir datos
        for emp in empleados:
            writer.writerow([
                emp.ficha,
                emp.nombre,
                emp.apellidos,
                emp.departamento_rel.nombre if emp.departamento_rel else '',
                emp.cargo,
                emp.turno_rel.tipo if hasattr(emp, 'turno_rel') and emp.turno_rel else emp.turno,
                'Activo' if emp.activo else 'Inactivo',
                emp.fecha_ingreso.strftime('%d/%m/%Y') if emp.fecha_ingreso else '',
                emp.sexo,
                emp.observaciones
            ])
        
        # Preparar la respuesta
        output.seek(0)
        return Response(
            output,
            mimetype='text/csv',
            headers={
                'Content-Disposition': f'attachment; filename=empleados_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
            }
        )
        
    except Exception as e:
        flash(f'Error al exportar empleados: {str(e)}', 'error')
        return redirect(url_for('employees.list_employees'))

@employees_bp.route('/exportar-excel')
def export_employees_excel():
    """Exportar empleados a Excel"""
    try:
        # Obtener los parámetros de filtro de la solicitud
        filtros = {
            'departamento': request.args.get('departamento', ''),
            'cargo': request.args.get('cargo', ''),
            'estado': request.args.get('estado', ''),
            'turno': request.args.get('turno', ''),
            'busqueda': request.args.get('busqueda', ''),
            'excluir_encargados': request.args.get('excluir_encargados', '') == 'on',
            'solo_bajas_medicas': request.args.get('solo_bajas_medicas', '') == 'on',
            'solo_disponibles': request.args.get('solo_disponibles', '') == 'on',
            'fecha_ingreso_desde': request.args.get('fecha_ingreso_desde', ''),
            'fecha_ingreso_hasta': request.args.get('fecha_ingreso_hasta', ''),
            'antiguedad_min': request.args.get('antiguedad_min', type=int),
            'antiguedad_max': request.args.get('antiguedad_max', type=int)
        }
        
        # Obtener empleados filtrados usando el servicio
        resultado = employee_service.get_filtered_employees(**filtros)
        empleados = resultado['empleados']
        
        # Crear el archivo Excel en memoria
        output = io.BytesIO()
        workbook = openpyxl.Workbook()
        sheet = workbook.active
        sheet.title = "Empleados"
        
        # Estilos para el encabezado
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
        
        # Escribir encabezados
        headers = [
            'Ficha', 'Nombre', 'Apellidos', 'Departamento', 'Cargo', 
            'Turno', 'Estado', 'Fecha Ingreso', 'Sexo', 'Observaciones'
        ]
        sheet.append(headers)
        
        # Aplicar estilo al encabezado
        for cell in sheet[sheet.max_row]:
            cell.font = header_font
            cell.fill = header_fill
        
        # Escribir datos
        for emp in empleados:
            sheet.append([
                emp.ficha,
                emp.nombre,
                emp.apellidos,
                emp.departamento_rel.nombre if emp.departamento_rel else '',
                emp.cargo,
                emp.turno_rel.tipo if hasattr(emp, 'turno_rel') and emp.turno_rel else emp.turno,
                'Activo' if emp.activo else 'Inactivo',
                emp.fecha_ingreso.strftime('%d/%m/%Y') if emp.fecha_ingreso else '',
                emp.sexo,
                emp.observaciones
            ])
        
        # Ajustar ancho de columnas
        for column in sheet.columns:
            max_length = 0
            column = [cell for cell in column]
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            sheet.column_dimensions[get_column_letter(column[0].column)].width = adjusted_width
        
        # Guardar el archivo
        workbook.save(output)
        output.seek(0)
        
        return Response(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={
                'Content-Disposition': f'attachment; filename=empleados_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            }
        )
        
    except Exception as e:
        flash(f'Error al exportar empleados: {str(e)}', 'error')
        return redirect(url_for('employees.list_employees'))

@employees_bp.route('/exportar-pdf')
def export_employees_pdf():
    """Exportar empleados a PDF"""
    try:
        # Obtener los parámetros de filtro de la solicitud
        filtros = {
            'departamento': request.args.get('departamento', ''),
            'cargo': request.args.get('cargo', ''),
            'estado': request.args.get('estado', ''),
            'turno': request.args.get('turno', ''),
            'busqueda': request.args.get('busqueda', ''),
            'excluir_encargados': request.args.get('excluir_encargados', '') == 'on',
            'solo_bajas_medicas': request.args.get('solo_bajas_medicas', '') == 'on',
            'solo_disponibles': request.args.get('solo_disponibles', '') == 'on',
            'fecha_ingreso_desde': request.args.get('fecha_ingreso_desde', ''),
            'fecha_ingreso_hasta': request.args.get('fecha_ingreso_hasta', ''),
            'antiguedad_min': request.args.get('antiguedad_min', type=int),
            'antiguedad_max': request.args.get('antiguedad_max', type=int)
        }
        
        # Obtener empleados filtrados usando el servicio
        resultado = employee_service.get_filtered_employees(**filtros)
        empleados = resultado['empleados']
        
        # Crear el PDF
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=landscape(A4))
        elements = []
        
        # Estilos
        styles = getSampleStyleSheet()
        style_normal = styles["Normal"]
        style_heading = styles["Heading1"]
        
        # Título
        elements.append(Paragraph("Listado de Empleados", style_heading))
        elements.append(Spacer(1, 20))
        
        # Tabla de datos
        data = [['Ficha', 'Nombre', 'Apellidos', 'Departamento', 'Cargo', 'Turno', 'Estado', 'Fecha Ingreso', 'Sexo', 'Observaciones']]
        
        for emp in empleados:
            data.append([
                str(emp.ficha),
                emp.nombre,
                emp.apellidos,
                emp.departamento_rel.nombre if emp.departamento_rel else '',
                emp.cargo,
                emp.turno_rel.tipo if hasattr(emp, 'turno_rel') and emp.turno_rel else emp.turno,
                'Activo' if emp.activo else 'Inactivo',
                emp.fecha_ingreso.strftime('%d/%m/%Y') if emp.fecha_ingreso else '',
                emp.sexo,
                emp.observaciones
            ])
        
        # Crear tabla
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.blue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        elements.append(table)
        
        # Construir PDF
        doc.build(elements)
        buffer.seek(0)
        
        return Response(
            buffer,
            mimetype='application/pdf',
            headers={
                'Content-Disposition': f'attachment; filename=empleados_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf'
            }
        )
        
    except Exception as e:
        flash(f'Error al exportar empleados: {str(e)}', 'error')
        return redirect(url_for('employees.list_employees'))

@employees_bp.route('/exportaciones')
def listar_exportaciones():
    """Listar las exportaciones guardadas"""
    try:
        # Obtener la lista de archivos en el directorio de exportaciones
        export_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'exports')
        if not os.path.exists(export_dir):
            os.makedirs(export_dir)
            
        archivos = []
        for filename in os.listdir(export_dir):
            if filename.endswith(('.csv', '.xlsx', '.pdf')):
                filepath = os.path.join(export_dir, filename)
                archivos.append({
                    'nombre': filename,
                    'ruta': filepath,
                    'tamaño': os.path.getsize(filepath),
                    'fecha': datetime.fromtimestamp(os.path.getmtime(filepath))
                })
        
        # Ordenar por fecha de modificación (más reciente primero)
        archivos.sort(key=lambda x: x['fecha'], reverse=True)
        
        return render_template('exportaciones.html', archivos=archivos)
        
    except Exception as e:
        flash(f'Error al listar exportaciones: {str(e)}', 'error')
        return redirect(url_for('employees.list_employees'))

@employees_bp.route('/exportaciones/descargar/<path:filename>')
def descargar_exportacion(filename):
    """Descargar una exportación guardada"""
    try:
        export_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'exports')
        return send_from_directory(export_dir, filename, as_attachment=True)
    except Exception as e:
        flash(f'Error al descargar el archivo: {str(e)}', 'error')
        return redirect(url_for('employees.listar_exportaciones'))

@employees_bp.route('/exportaciones/eliminar/<path:filename>', methods=['POST'])
def eliminar_exportacion(filename):
    """Eliminar una exportación guardada"""
    try:
        export_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'exports')
        filepath = os.path.join(export_dir, filename)
        
        if os.path.exists(filepath):
            os.remove(filepath)
            flash('Archivo eliminado correctamente', 'success')
        else:
            flash('El archivo no existe', 'error')
            
        return redirect(url_for('employees.listar_exportaciones'))
        
    except Exception as e:
        flash(f'Error al eliminar el archivo: {str(e)}', 'error')
        return redirect(url_for('employees.listar_exportaciones'))

@employees_bp.route('/api/buscar-empleado')
def api_buscar_empleado():
    from flask import request, jsonify
    from models import Empleado, Sector
    texto = request.args.get('q', '').strip().lower()
    if not texto:
        return jsonify([])
    # Buscar en ficha, nombre, apellidos, cargo, sector
    query = Empleado.query.join(Sector, Empleado.sector_id == Sector.id)
    like = f"%{texto}%"
    empleados = query.filter(
        or_(
            func.cast(Empleado.ficha, db.String).ilike(like),
            Empleado.nombre.ilike(like),
            Empleado.apellidos.ilike(like),
            Empleado.cargo.ilike(like),
            Sector.nombre.ilike(like)
        )
    ).limit(10).all()
    resultados = [
        {
            'id': e.id,
            'ficha': e.ficha,
            'nombre': f"{e.nombre} {e.apellidos}",
            'cargo': e.cargo,
            'sector': e.sector_rel.nombre if e.sector_rel else ''
        }
        for e in empleados
    ]
    return jsonify(resultados)
    