import type { HasProps } from "../core/has_props";
import type { Color, Arrayable } from "../core/types";
import type { Class } from "../core/class";
import type { Vector } from "../core/vectorization";
import type { VectorSpec, ScalarSpec, ColorSpec, UnitsSpec, Property } from "../core/properties";
import type { RenderLevel } from "../core/enums";
import type * as nd from "../core/util/ndarray";
import type { Glyph, Glyph<PERSON><PERSON>er, ColumnarDataSource, CDSView, CoordinateMapping } from "./models";
import { AnnularWedge, Annulus, Arc, Bezier, Block, Circle, Ellipse, HArea, HAreaStep, HBar, HSpan, HStrip, HexTile, Image, ImageRGBA, ImageStack, ImageURL, Line, MathMLGlyph as MathML, MultiLine, MultiPolygons, Ngon, Patch, Patches, Quad, Quadratic, Ray, Rect, <PERSON>atter, Segment, <PERSON>p<PERSON>, <PERSON>, TeXGlyph as Te<PERSON>, Text, VArea, VAreaStep, VBar, VSpan, VStrip, Wedge } from "../models/glyphs";
import type { Marker } from "../models/glyphs/marker";
export type NamesOf<T extends HasProps> = (Extract<keyof T["properties"], string>)[];
export type ColorNDArray = nd.Uint32Array1d | nd.Uint8Array1d | nd.Uint8Array2d | nd.FloatArray2d | nd.ObjectNDArray;
export type VectorArg<T> = T | Arrayable<T> | Vector<T>;
export type ColorArg = VectorArg<Color | null> | ColorNDArray;
export type AlphaArg = VectorArg<number>;
export type ColorAlpha = {
    color: ColorArg;
    selection_color: ColorArg;
    nonselection_color: ColorArg;
    hover_color: ColorArg;
    muted_color: ColorArg;
    alpha: AlphaArg;
    selection_alpha: AlphaArg;
    nonselection_alpha: AlphaArg;
    hover_alpha: AlphaArg;
    muted_alpha: AlphaArg;
};
export type AuxHatch = {
    selection_hatch_color: ColorArg;
    selection_hatch_alpha: AlphaArg;
    nonselection_hatch_color: ColorArg;
    nonselection_hatch_alpha: AlphaArg;
    hover_hatch_color: ColorArg;
    hover_hatch_alpha: AlphaArg;
    muted_hatch_color: ColorArg;
    muted_hatch_alpha: AlphaArg;
};
export type AuxFill = {
    selection_fill_color: ColorArg;
    selection_fill_alpha: AlphaArg;
    nonselection_fill_color: ColorArg;
    nonselection_fill_alpha: AlphaArg;
    hover_fill_color: ColorArg;
    hover_fill_alpha: AlphaArg;
    muted_fill_color: ColorArg;
    muted_fill_alpha: AlphaArg;
};
export type AuxLine = {
    selection_line_color: ColorArg;
    selection_line_alpha: AlphaArg;
    nonselection_line_color: ColorArg;
    nonselection_line_alpha: AlphaArg;
    hover_line_color: ColorArg;
    hover_line_alpha: AlphaArg;
    muted_line_color: ColorArg;
    muted_line_alpha: AlphaArg;
};
export type AuxText = {
    selection_text_color: ColorArg;
    selection_text_alpha: AlphaArg;
    nonselection_text_color: ColorArg;
    nonselection_text_alpha: AlphaArg;
    hover_text_color: ColorArg;
    hover_text_alpha: AlphaArg;
    muted_text_color: ColorArg;
    muted_text_alpha: AlphaArg;
};
export type AuxGlyph = {
    source: ColumnarDataSource | ColumnarDataSource["data"];
    view: CDSView;
    legend_label: string;
    legend_field: string;
    legend_group: string;
    level: RenderLevel;
    name: string;
    visible: boolean;
    x_range_name: string;
    y_range_name: string;
    coordinates: CoordinateMapping | null;
};
export type ArgsOf<P> = {
    [K in keyof P]: (P[K] extends ColorSpec ? ColorArg : (P[K] extends VectorSpec<infer T, infer V> ? T | Arrayable<T> | V : (P[K] extends ScalarSpec<infer T, infer S> ? T | S : (P[K] extends Property<infer T> ? T : never))));
};
export type UnitsOf<P> = {
    [K in keyof P & string as `${K}_units`]: P[K] extends UnitsSpec<any, infer Units> ? Units : never;
};
export type GlyphArgs<P> = ArgsOf<P> & UnitsOf<P> & AuxGlyph & ColorAlpha;
export type AnnularWedgeArgs = GlyphArgs<AnnularWedge.Props> & AuxLine & AuxFill & AuxHatch;
export type AnnulusArgs = GlyphArgs<Annulus.Props> & AuxLine & AuxFill & AuxHatch;
export type ArcArgs = GlyphArgs<Arc.Props> & AuxLine;
export type BezierArgs = GlyphArgs<Bezier.Props> & AuxLine;
export type BlockArgs = GlyphArgs<Block.Props> & AuxLine & AuxFill & AuxHatch;
export type CircleArgs = GlyphArgs<Circle.Props> & AuxLine & AuxFill & AuxHatch;
export type EllipseArgs = GlyphArgs<Ellipse.Props> & AuxLine & AuxFill & AuxHatch;
export type HAreaArgs = GlyphArgs<HArea.Props> & AuxFill & AuxHatch;
export type HAreaStepArgs = GlyphArgs<HAreaStep.Props> & AuxFill & AuxHatch;
export type HBarArgs = GlyphArgs<HBar.Props> & AuxLine & AuxFill & AuxHatch;
export type HSpanArgs = GlyphArgs<HSpan.Props> & AuxLine;
export type HStripArgs = GlyphArgs<HStrip.Props> & AuxLine & AuxFill & AuxHatch;
export type HexTileArgs = GlyphArgs<HexTile.Props> & AuxLine & AuxFill & AuxHatch;
export type ImageArgs = GlyphArgs<Image.Props>;
export type ImageRGBAArgs = GlyphArgs<ImageRGBA.Props>;
export type ImageStackArgs = GlyphArgs<ImageStack.Props>;
export type ImageURLArgs = GlyphArgs<ImageURL.Props>;
export type LineArgs = GlyphArgs<Line.Props> & AuxLine;
export type MarkerArgs = GlyphArgs<Marker.Props> & AuxLine & AuxFill & AuxHatch;
export type MathMLArgs = GlyphArgs<MathML.Props> & AuxText;
export type MultiLineArgs = GlyphArgs<MultiLine.Props> & AuxLine;
export type MultiPolygonsArgs = GlyphArgs<MultiPolygons.Props> & AuxLine & AuxFill & AuxHatch;
export type NgonArgs = GlyphArgs<Ngon.Props> & AuxLine & AuxFill & AuxHatch;
export type PatchArgs = GlyphArgs<Patch.Props> & AuxLine & AuxFill & AuxHatch;
export type PatchesArgs = GlyphArgs<Patches.Props> & AuxLine & AuxFill & AuxHatch;
export type QuadArgs = GlyphArgs<Quad.Props> & AuxLine & AuxFill & AuxHatch;
export type QuadraticArgs = GlyphArgs<Quadratic.Props> & AuxLine;
export type RayArgs = GlyphArgs<Ray.Props> & AuxLine;
export type RectArgs = GlyphArgs<Rect.Props> & AuxLine & AuxFill & AuxHatch;
export type ScatterArgs = GlyphArgs<Scatter.Props> & AuxLine & AuxFill & AuxHatch;
export type SegmentArgs = GlyphArgs<Segment.Props> & AuxLine;
export type SplineArgs = GlyphArgs<Spline.Props> & AuxLine;
export type StepArgs = GlyphArgs<Step.Props> & AuxLine;
export type TeXArgs = GlyphArgs<TeX.Props> & AuxText;
export type TextArgs = GlyphArgs<Text.Props> & AuxText;
export type VAreaArgs = GlyphArgs<VArea.Props> & AuxFill & AuxHatch;
export type VAreaStepArgs = GlyphArgs<VAreaStep.Props> & AuxFill & AuxHatch;
export type VBarArgs = GlyphArgs<VBar.Props> & AuxLine & AuxFill & AuxHatch;
export type VSpanArgs = GlyphArgs<VSpan.Props> & AuxLine;
export type VStripArgs = GlyphArgs<VStrip.Props> & AuxLine & AuxFill & AuxHatch;
export type WedgeArgs = GlyphArgs<Wedge.Props> & AuxLine & AuxFill & AuxHatch;
export declare abstract class GlyphAPI {
    abstract _glyph<G extends Glyph>(cls: Class<G>, method: string, positional: NamesOf<G>, args: unknown[], overrides?: object): GlyphRenderer<G>;
    annular_wedge(): GlyphRenderer<AnnularWedge>;
    annular_wedge(args: Partial<AnnularWedgeArgs>): GlyphRenderer<AnnularWedge>;
    annular_wedge(x: AnnularWedgeArgs["x"], y: AnnularWedgeArgs["y"], inner_radius: AnnularWedgeArgs["inner_radius"], outer_radius: AnnularWedgeArgs["outer_radius"], start_angle: AnnularWedgeArgs["start_angle"], end_angle: AnnularWedgeArgs["end_angle"], args?: Partial<AnnularWedgeArgs>): GlyphRenderer<AnnularWedge>;
    annulus(): GlyphRenderer<Annulus>;
    annulus(args: Partial<AnnulusArgs>): GlyphRenderer<Annulus>;
    annulus(x: AnnulusArgs["x"], y: AnnulusArgs["y"], inner_radius: AnnulusArgs["inner_radius"], outer_radius: AnnulusArgs["outer_radius"], args?: Partial<AnnulusArgs>): GlyphRenderer<Annulus>;
    arc(): GlyphRenderer<Arc>;
    arc(args: Partial<ArcArgs>): GlyphRenderer<Arc>;
    arc(x: ArcArgs["x"], y: ArcArgs["y"], radius: ArcArgs["radius"], start_angle: ArcArgs["start_angle"], end_angle: ArcArgs["end_angle"], args?: Partial<ArcArgs>): GlyphRenderer<Arc>;
    bezier(): GlyphRenderer<Bezier>;
    bezier(args: Partial<BezierArgs>): GlyphRenderer<Bezier>;
    bezier(x0: BezierArgs["x0"], y0: BezierArgs["y0"], x1: BezierArgs["x1"], y1: BezierArgs["y1"], cx0: BezierArgs["cx0"], cy0: BezierArgs["cy0"], cx1: BezierArgs["cx1"], cy1: BezierArgs["cy1"], args?: Partial<BezierArgs>): GlyphRenderer<Bezier>;
    block(): GlyphRenderer<Block>;
    block(args: Partial<BlockArgs>): GlyphRenderer<Block>;
    block(x: BlockArgs["x"], y: BlockArgs["y"], width: BlockArgs["width"], height: BlockArgs["height"], args?: Partial<BlockArgs>): GlyphRenderer<Block>;
    circle(): GlyphRenderer<Circle>;
    circle(args: Partial<CircleArgs>): GlyphRenderer<Circle>;
    circle(x: CircleArgs["x"], y: CircleArgs["y"], radius: CircleArgs["radius"], args?: Partial<CircleArgs>): GlyphRenderer<Circle>;
    ellipse(): GlyphRenderer<Ellipse>;
    ellipse(args: Partial<EllipseArgs>): GlyphRenderer<Ellipse>;
    ellipse(x: EllipseArgs["x"], y: EllipseArgs["y"], width: EllipseArgs["width"], height: EllipseArgs["height"], args?: Partial<EllipseArgs>): GlyphRenderer<Ellipse>;
    harea(): GlyphRenderer<HArea>;
    harea(args: Partial<HAreaArgs>): GlyphRenderer<HArea>;
    harea(x1: HAreaArgs["x1"], x2: HAreaArgs["x2"], y: HAreaArgs["y"], args?: Partial<HAreaArgs>): GlyphRenderer<HArea>;
    harea_step(): GlyphRenderer<HAreaStep>;
    harea_step(args: Partial<HAreaStepArgs>): GlyphRenderer<HAreaStep>;
    harea_step(x1: HAreaStepArgs["x1"], x2: HAreaStepArgs["x2"], y: HAreaStepArgs["y"], step_mode: HAreaStepArgs["step_mode"], args?: Partial<HAreaStepArgs>): GlyphRenderer<HAreaStep>;
    hbar(): GlyphRenderer<HBar>;
    hbar(args: Partial<HBarArgs>): GlyphRenderer<HBar>;
    hbar(y: HBarArgs["y"], height: HBarArgs["height"], right: HBarArgs["right"], left: HBarArgs["left"], args?: Partial<HBarArgs>): GlyphRenderer<HBar>;
    hspan(): GlyphRenderer<HSpan>;
    hspan(args: Partial<HSpanArgs>): GlyphRenderer<HSpan>;
    hspan(y: HSpanArgs["y"], args?: Partial<HSpanArgs>): GlyphRenderer<HSpan>;
    hstrip(): GlyphRenderer<HStrip>;
    hstrip(args: Partial<HStripArgs>): GlyphRenderer<HStrip>;
    hstrip(y0: HStripArgs["y0"], y1: HStripArgs["y1"], args?: Partial<HStripArgs>): GlyphRenderer<HStrip>;
    hex_tile(): GlyphRenderer<HexTile>;
    hex_tile(args: Partial<HexTileArgs>): GlyphRenderer<HexTile>;
    hex_tile(q: HexTileArgs["q"], r: HexTileArgs["r"], args?: Partial<HexTileArgs>): GlyphRenderer<HexTile>;
    image(): GlyphRenderer<Image>;
    image(args: Partial<ImageArgs>): GlyphRenderer<Image>;
    image(image: ImageArgs["image"], x: ImageArgs["x"], y: ImageArgs["y"], dw: ImageArgs["dw"], dh: ImageArgs["dh"], args?: Partial<ImageArgs>): GlyphRenderer<Image>;
    image_stack(): GlyphRenderer<ImageStack>;
    image_stack(args: Partial<ImageStackArgs>): GlyphRenderer<ImageStack>;
    image_stack(image: ImageStackArgs["image"], x: ImageStackArgs["x"], y: ImageStackArgs["y"], dw: ImageStackArgs["dw"], dh: ImageStackArgs["dh"], args?: Partial<ImageStackArgs>): GlyphRenderer<ImageStack>;
    image_rgba(): GlyphRenderer<ImageRGBA>;
    image_rgba(args: Partial<ImageRGBAArgs>): GlyphRenderer<ImageRGBA>;
    image_rgba(image: ImageRGBAArgs["image"], x: ImageRGBAArgs["x"], y: ImageRGBAArgs["y"], dw: ImageRGBAArgs["dw"], dh: ImageRGBAArgs["dh"], args?: Partial<ImageRGBAArgs>): GlyphRenderer<ImageRGBA>;
    image_url(): GlyphRenderer<ImageURL>;
    image_url(args: Partial<ImageURLArgs>): GlyphRenderer<ImageURL>;
    image_url(url: ImageURLArgs["url"], x: ImageURLArgs["x"], y: ImageURLArgs["y"], w: ImageURLArgs["w"], h: ImageURLArgs["h"], args?: Partial<ImageURLArgs>): GlyphRenderer<ImageURL>;
    line(): GlyphRenderer<Line>;
    line(args: Partial<LineArgs>): GlyphRenderer<Line>;
    line(x: LineArgs["x"], y: LineArgs["y"], args?: Partial<LineArgs>): GlyphRenderer<Line>;
    mathml(): GlyphRenderer<MathML>;
    mathml(args: Partial<MathMLArgs>): GlyphRenderer<MathML>;
    mathml(x: MathMLArgs["x"], y: MathMLArgs["y"], text: MathMLArgs["text"], args?: Partial<MathMLArgs>): GlyphRenderer<MathML>;
    multi_line(): GlyphRenderer<MultiLine>;
    multi_line(args: Partial<MultiLineArgs>): GlyphRenderer<MultiLine>;
    multi_line(xs: MultiLineArgs["xs"], ys: MultiLineArgs["ys"], args?: Partial<MultiLineArgs>): GlyphRenderer<MultiLine>;
    multi_polygons(): GlyphRenderer<MultiPolygons>;
    multi_polygons(args: Partial<MultiPolygonsArgs>): GlyphRenderer<MultiPolygons>;
    multi_polygons(xs: MultiPolygonsArgs["xs"], ys: MultiPolygonsArgs["ys"], args?: Partial<MultiPolygonsArgs>): GlyphRenderer<MultiPolygons>;
    ngon(): GlyphRenderer<Ngon>;
    ngon(args: Partial<NgonArgs>): GlyphRenderer<Ngon>;
    ngon(x: NgonArgs["x"], y: NgonArgs["y"], radius: NgonArgs["radius"], args?: Partial<NgonArgs>): GlyphRenderer<Ngon>;
    patch(): GlyphRenderer<Patch>;
    patch(args: Partial<PatchArgs>): GlyphRenderer<Patch>;
    patch(x: PatchArgs["x"], y: PatchArgs["y"], args?: Partial<PatchArgs>): GlyphRenderer<Patch>;
    patches(): GlyphRenderer<Patches>;
    patches(args: Partial<PatchesArgs>): GlyphRenderer<Patches>;
    patches(xs: PatchesArgs["xs"], ys: PatchesArgs["ys"], args?: Partial<PatchesArgs>): GlyphRenderer<Patches>;
    quad(): GlyphRenderer<Quad>;
    quad(args: Partial<QuadArgs>): GlyphRenderer<Quad>;
    quad(left: QuadArgs["left"], right: QuadArgs["right"], bottom: QuadArgs["bottom"], top: QuadArgs["top"], args?: Partial<QuadArgs>): GlyphRenderer<Quad>;
    quadratic(): GlyphRenderer<Quadratic>;
    quadratic(args: Partial<QuadraticArgs>): GlyphRenderer<Quadratic>;
    quadratic(x0: QuadraticArgs["x0"], y0: QuadraticArgs["y0"], x1: QuadraticArgs["x1"], y1: QuadraticArgs["y1"], cx: QuadraticArgs["cx"], cy: QuadraticArgs["cy"], args?: Partial<QuadraticArgs>): GlyphRenderer<Quadratic>;
    ray(): GlyphRenderer<Ray>;
    ray(args: Partial<RayArgs>): GlyphRenderer<Ray>;
    ray(x: RayArgs["x"], y: RayArgs["y"], length: RayArgs["length"], args?: Partial<RayArgs>): GlyphRenderer<Ray>;
    rect(): GlyphRenderer<Rect>;
    rect(args: Partial<RectArgs>): GlyphRenderer<Rect>;
    rect(x: RectArgs["x"], y: RectArgs["y"], width: RectArgs["width"], height: RectArgs["height"], args?: Partial<RectArgs>): GlyphRenderer<Rect>;
    segment(): GlyphRenderer<Segment>;
    segment(args: Partial<SegmentArgs>): GlyphRenderer<Segment>;
    segment(x0: SegmentArgs["x0"], y0: SegmentArgs["y0"], x1: SegmentArgs["x1"], y1: SegmentArgs["y1"], args?: Partial<SegmentArgs>): GlyphRenderer<Segment>;
    spline(): GlyphRenderer<Spline>;
    spline(args: Partial<SplineArgs>): GlyphRenderer<Spline>;
    spline(x: SplineArgs["x"], y: SplineArgs["y"], args?: Partial<SplineArgs>): GlyphRenderer<Spline>;
    step(): GlyphRenderer<Step>;
    step(args: Partial<StepArgs>): GlyphRenderer<Step>;
    step(x: StepArgs["x"], y: StepArgs["y"], mode: StepArgs["mode"], args?: Partial<StepArgs>): GlyphRenderer<Step>;
    tex(): GlyphRenderer<TeX>;
    tex(args: Partial<TeXArgs>): GlyphRenderer<TeX>;
    tex(x: TeXArgs["x"], y: TeXArgs["y"], text: TeXArgs["text"], args?: Partial<TeXArgs>): GlyphRenderer<TeX>;
    text(): GlyphRenderer<Text>;
    text(args: Partial<TextArgs>): GlyphRenderer<Text>;
    text(x: TextArgs["x"], y: TextArgs["y"], text: TextArgs["text"], args?: Partial<TextArgs>): GlyphRenderer<Text>;
    varea(): GlyphRenderer<VArea>;
    varea(args: Partial<VAreaArgs>): GlyphRenderer<VArea>;
    varea(x: VAreaArgs["x"], y1: VAreaArgs["y1"], y2: VAreaArgs["y2"], args?: Partial<VAreaArgs>): GlyphRenderer<VArea>;
    varea_step(): GlyphRenderer<VAreaStep>;
    varea_step(args: Partial<VAreaStepArgs>): GlyphRenderer<VAreaStep>;
    varea_step(x: VAreaStepArgs["x"], y1: VAreaStepArgs["y1"], y2: VAreaStepArgs["y2"], step_mode: VAreaStepArgs["step_mode"], args?: Partial<VAreaStepArgs>): GlyphRenderer<VAreaStep>;
    vbar(): GlyphRenderer<VBar>;
    vbar(args: Partial<VBarArgs>): GlyphRenderer<VBar>;
    vbar(x: VBarArgs["x"], width: VBarArgs["width"], top: VBarArgs["top"], bottom: VBarArgs["bottom"], args?: Partial<VBarArgs>): GlyphRenderer<VBar>;
    vspan(): GlyphRenderer<VSpan>;
    vspan(args: Partial<VSpanArgs>): GlyphRenderer<VSpan>;
    vspan(x: VSpanArgs["x"], args?: Partial<VSpanArgs>): GlyphRenderer<VSpan>;
    vstrip(): GlyphRenderer<VStrip>;
    vstrip(args: Partial<VStripArgs>): GlyphRenderer<VStrip>;
    vstrip(x0: VStripArgs["x0"], x1: VStripArgs["x1"], args?: Partial<VStripArgs>): GlyphRenderer<VStrip>;
    wedge(): GlyphRenderer<Wedge>;
    wedge(args: Partial<WedgeArgs>): GlyphRenderer<Wedge>;
    wedge(x: WedgeArgs["x"], y: WedgeArgs["y"], radius: WedgeArgs["radius"], start_angle: WedgeArgs["start_angle"], end_angle: WedgeArgs["end_angle"], args?: Partial<WedgeArgs>): GlyphRenderer<Wedge>;
    private _scatter;
    scatter(): GlyphRenderer<Scatter>;
    scatter(args: Partial<ScatterArgs>): GlyphRenderer<Scatter>;
    scatter(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<ScatterArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ asterisk(): GlyphRenderer<Scatter>;
    /** @deprecated */ asterisk(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ asterisk(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ circle_cross(): GlyphRenderer<Scatter>;
    /** @deprecated */ circle_cross(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ circle_cross(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ circle_dot(): GlyphRenderer<Scatter>;
    /** @deprecated */ circle_dot(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ circle_dot(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ circle_x(): GlyphRenderer<Scatter>;
    /** @deprecated */ circle_x(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ circle_x(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ circle_y(): GlyphRenderer<Scatter>;
    /** @deprecated */ circle_y(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ circle_y(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ cross(): GlyphRenderer<Scatter>;
    /** @deprecated */ cross(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ cross(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ dash(): GlyphRenderer<Scatter>;
    /** @deprecated */ dash(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ dash(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ diamond(): GlyphRenderer<Scatter>;
    /** @deprecated */ diamond(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ diamond(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ diamond_cross(): GlyphRenderer<Scatter>;
    /** @deprecated */ diamond_cross(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ diamond_cross(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ diamond_dot(): GlyphRenderer<Scatter>;
    /** @deprecated */ diamond_dot(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ diamond_dot(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ dot(): GlyphRenderer<Scatter>;
    /** @deprecated */ dot(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ dot(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ hex(): GlyphRenderer<Scatter>;
    /** @deprecated */ hex(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ hex(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ hex_dot(): GlyphRenderer<Scatter>;
    /** @deprecated */ hex_dot(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ hex_dot(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ inverted_triangle(): GlyphRenderer<Scatter>;
    /** @deprecated */ inverted_triangle(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ inverted_triangle(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ plus(): GlyphRenderer<Scatter>;
    /** @deprecated */ plus(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ plus(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ square(): GlyphRenderer<Scatter>;
    /** @deprecated */ square(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ square(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ square_cross(): GlyphRenderer<Scatter>;
    /** @deprecated */ square_cross(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ square_cross(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ square_dot(): GlyphRenderer<Scatter>;
    /** @deprecated */ square_dot(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ square_dot(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ square_pin(): GlyphRenderer<Scatter>;
    /** @deprecated */ square_pin(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ square_pin(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ square_x(): GlyphRenderer<Scatter>;
    /** @deprecated */ square_x(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ square_x(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ star(): GlyphRenderer<Scatter>;
    /** @deprecated */ star(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ star(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ star_dot(): GlyphRenderer<Scatter>;
    /** @deprecated */ star_dot(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ star_dot(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ triangle(): GlyphRenderer<Scatter>;
    /** @deprecated */ triangle(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ triangle(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ triangle_dot(): GlyphRenderer<Scatter>;
    /** @deprecated */ triangle_dot(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ triangle_dot(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ triangle_pin(): GlyphRenderer<Scatter>;
    /** @deprecated */ triangle_pin(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ triangle_pin(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ x(): GlyphRenderer<Scatter>;
    /** @deprecated */ x(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ x(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ y(): GlyphRenderer<Scatter>;
    /** @deprecated */ y(args: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
    /** @deprecated */ y(x: MarkerArgs["x"], y: MarkerArgs["y"], args?: Partial<MarkerArgs>): GlyphRenderer<Scatter>;
}
//# sourceMappingURL=glyph_api.d.ts.map