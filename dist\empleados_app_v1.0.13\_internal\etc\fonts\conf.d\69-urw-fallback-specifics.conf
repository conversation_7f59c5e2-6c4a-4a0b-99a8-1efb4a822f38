<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">

<!--
This file is used to alias/map previous versions of font families from (URW)++
to similar/metric-compatible font families - in this case as original PostScript
fonts (as specifics).

NOTE: These mappings are already part of fontconfig's config files by default.
      We are keeping this file just to be complete, or if some distribution
      needs it in some special case.

Most likely this aliasing/mapping will be useful for people who:
 * have documents referencing older versions of the (URW)++ fonts, and need to
   map their similar/metric-compatible fonts via generic font names

PostScript fonts:       latest URW fonts:     previous URW fonts:
======================  ====================  =============================================
Courier                 Nimbus Mono PS        Nimbus Mono L | Nimbus Mono
ITC Avant Garde Gothic  URW Gothic            URW Gothic L
ITC Bookman             URW Bookman           URW Bookman L | Bookman URW
ITC Zapf Chancery       Z003                  URW Chancery L | Chancery URW
ITC Zapf Dingbats       D050000L              Dingbats
Helvetica               Nimbus Sans           Nimbus Sans L
Helvetica Narrow        Nimbus Sans Narrow    Nimbus Sans Narrow (same as current name)
New Century Schoolbook  C059                  Century Schoolbook L | Century SchoolBook URW
Palatino                P052                  URW Palladio L | Palladio URW
Symbol                  Standard Symbols PS   Standard Symbols L
Times                   Nimbus Roman          Nimbus Roman No9 L
-->

<fontconfig>
  <!-- Original PostScript base font mapping -->
  <alias binding="same">
    <family>Nimbus Mono</family>
    <default>
      <family>Courier</family>
    </default>
  </alias>

  <alias binding="same">
    <family>Nimbus Mono L</family>
    <default>
      <family>Courier</family>
    </default>
  </alias>

  <alias binding="same">
    <family>Nimbus Sans L</family>
    <default>
      <family>Helvetica</family>
    </default>
  </alias>

  <alias binding="same">
    <family>URW Gothic L</family>
    <default>
      <family>ITC Avant Garde Gothic</family>
    </default>
  </alias>

  <alias binding="same">
    <family>Bookman URW</family>
    <default>
      <family>ITC Bookman</family>
    </default>
  </alias>

  <alias binding="same">
    <family>URW Bookman L</family>
    <default>
      <family>ITC Bookman</family>
    </default>
  </alias>

  <alias binding="same">
    <family>Chancery URW</family>
    <default>
      <family>ITC Zapf Chancery</family>
    </default>
  </alias>

  <alias binding="same">
    <family>URW Chancery L</family>
    <default>
      <family>ITC Zapf Chancery</family>
    </default>
  </alias>

  <alias binding="same">
    <family>Dingbats</family>
    <default>
      <family>ITC Zapf Dingbats</family>
    </default>
  </alias>

  <alias binding="same">
    <family>Century Schoolbook L</family>
    <default>
      <family>New Century Schoolbook</family>
    </default>
  </alias>

  <alias binding="same">
    <family>Century SchoolBook URW</family>
    <default>
      <family>New Century Schoolbook</family>
    </default>
  </alias>

  <alias binding="same">
    <family>Palladio URW</family>
    <default>
      <family>Palatino</family>
    </default>
  </alias>

  <alias binding="same">
    <family>URW Palladio L</family>
    <default>
      <family>Palatino</family>
    </default>
  </alias>

  <alias binding="same">
    <family>Standard Symbols L</family>
    <default>
      <family>Symbol</family>
    </default>
  </alias>

  <alias binding="same">
    <family>Nimbus Roman No9 L</family>
    <default>
      <family>Times</family>
    </default>
  </alias>
</fontconfig>
