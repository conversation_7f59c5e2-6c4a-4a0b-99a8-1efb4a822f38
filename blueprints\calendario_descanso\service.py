from .models import Descanso, db
from datetime import date

def asignar_descanso_sector(turno_id, fecha, creado_por):
    # Asigna día de descanso a todos los empleados activos del turno para la fecha dada
    from models import Empleado
    empleados = Empleado.query.filter_by(activo=True, turno_id=turno_id).all()
    for emp in empleados:
        descanso = Descanso.query.filter_by(empleado_id=emp.id, fecha=fecha).first()
        if not descanso:
            descanso = Descanso(empleado_id=emp.id, sector_id=emp.sector_id, fecha=fecha, es_descanso=True, creado_por=creado_por)
            db.session.add(descanso)
        else:
            descanso.es_descanso = True
            descanso.creado_por = creado_por
    db.session.commit()
    return True

def editar_descanso_empleado(empleado_id, fecha, es_descanso, sector_id, observacion, creado_por):
    descanso = Descanso.query.filter_by(empleado_id=empleado_id, fecha=fecha).first()
    if not descanso:
        descanso = Descanso(empleado_id=empleado_id, sector_id=sector_id, fecha=fecha, es_descanso=es_descanso, observacion=observacion, creado_por=creado_por)
        db.session.add(descanso)
    else:
        descanso.es_descanso = es_descanso
        descanso.sector_id = sector_id
        descanso.observacion = observacion
        descanso.creado_por = creado_por
    db.session.commit()
    return True

def obtener_descansos(turno_id, fecha_inicio, fecha_fin):
    from models import Empleado
    empleados = Empleado.query.filter_by(activo=True, turno_id=turno_id).all()
    empleados_ids = [e.id for e in empleados]
    descansos = Descanso.query.filter(Descanso.empleado_id.in_(empleados_ids), Descanso.fecha >= fecha_inicio, Descanso.fecha <= fecha_fin).all()
    return [
        {
            'empleado_id': d.empleado_id,
            'fecha': d.fecha.strftime('%Y-%m-%d'),
            'es_descanso': d.es_descanso,
            'sector_id': d.sector_id,
            'observacion': d.observacion or ''
        }
        for d in descansos
    ] 