# Design Document

## Overview

This design addresses the critical bug in the competency by seniority report feature where users encounter an error when trying to access competency analysis charts. The primary issue is a missing `date` import in the `CompetenceDistributionService` class, which causes a `NameError` when the system attempts to validate employee hire dates during data processing.

The fix involves correcting the import statement and ensuring robust error handling throughout the data processing pipeline to prevent similar issues in the future.

## Architecture

The competency by seniority feature follows a layered architecture:

1. **Presentation Layer**: `blueprints/statistics/routes.py` - Handles HTTP requests and renders templates
2. **Service Layer**: `services/competence_distribution_service.py` - Business logic for competency analysis
3. **Chart Generation Layer**: `services/matplotlib_chart_service.py` - Generates visual charts
4. **Data Layer**: Models (`Empleado`, `Polivalencia`, `Departamento`, `Sector`) - Database entities

The error occurs in the Service Layer during date validation, preventing the entire pipeline from completing successfully.

## Components and Interfaces

### CompetenceDistributionService

**Current Issue**: Missing `date` import causes `NameError` at line 178
**Location**: `services/competence_distribution_service.py`

**Key Methods**:
- `get_competence_by_seniority(department_id=None, sector_id=None)` - Main method that fails
- `_empty_response(seniority_ranges)` - Returns empty data structure when no data available

**Dependencies**:
- `datetime.datetime` (currently imported)
- `datetime.date` (missing import - root cause)
- `pandas` for data processing
- `numpy` for statistical calculations
- Database models via SQLAlchemy

### Statistics Routes

**Location**: `blueprints/statistics/routes.py`
**Route**: `/polivalencia/competencias-antiguedad`
**Method**: `competence_seniority()`

**Error Handling Flow**:
1. Calls `competence_distribution_service.get_competence_by_seniority()`
2. If exception occurs, catches it and displays error message
3. Redirects to main polivalencia page

### Chart Generation Service

**Location**: `services/matplotlib_chart_service.py`
**Key Methods**:
- `generate_competence_seniority_chart(competence_data)` - Creates bar chart
- `generate_competence_scatter_chart(competence_data)` - Creates scatter plot

## Data Models

### Employee Seniority Calculation

The system calculates employee seniority using:
```python
antiguedad_dias = (today - emp.fecha_ingreso).days
antiguedad = antiguedad_dias / 365.25  # Convert to years
```

### Seniority Ranges

The system uses predefined seniority ranges:
- Menos de 1 año (0-1 years)
- 1-3 años (1-3 years)
- 3-5 años (3-5 years)
- 5-10 años (5-10 years)
- 10-15 años (10-15 years)
- 15-20 años (15-20 years)
- +20 años (20+ years)

### Data Processing Pipeline

1. **Query Construction**: Build SQLAlchemy query with joins
2. **Filter Application**: Apply department/sector filters if provided
3. **Data Validation**: Validate employee hire dates (fails here due to missing import)
4. **Seniority Calculation**: Calculate years of service for each employee
5. **Aggregation**: Group employees by seniority ranges and calculate averages
6. **Correlation Analysis**: Calculate correlation between seniority and competency level
7. **Chart Data Preparation**: Format data for visualization

## Error Handling

### Current Error Handling Issues

1. **Import Error**: `NameError: name 'date' is not defined` at line 178
2. **Insufficient Error Context**: Generic error messages don't help users understand the issue
3. **Cache Issues**: Cached data might contain stale information

### Improved Error Handling Strategy

1. **Import Fix**: Add missing `date` import
2. **Granular Exception Handling**: Catch specific exceptions at each processing stage
3. **User-Friendly Messages**: Provide contextual error messages
4. **Logging Enhancement**: Add detailed logging for debugging
5. **Graceful Degradation**: Continue processing when individual records fail

## Testing Strategy

### Unit Tests

1. **Import Validation**: Test that all required imports are available
2. **Date Validation**: Test date type checking with various date formats
3. **Seniority Calculation**: Test age calculation with edge cases
4. **Empty Data Handling**: Test behavior when no employees match criteria
5. **Filter Validation**: Test department and sector filtering

### Integration Tests

1. **End-to-End Route Testing**: Test complete request flow from route to response
2. **Chart Generation**: Test chart generation with various data scenarios
3. **Error Recovery**: Test error handling and user feedback
4. **Cache Behavior**: Test cache invalidation and refresh

### Test Data Scenarios

1. **Normal Case**: Employees with valid hire dates and polivalency records
2. **Edge Cases**: 
   - Employees with future hire dates
   - Employees with null hire dates
   - Employees without polivalency records
3. **Filter Cases**:
   - Valid department/sector filters
   - Invalid filter IDs
   - Filters that return no results

## Performance Considerations

### Current Performance Issues

1. **Cache Timeout**: 300-second cache might be too short for expensive queries
2. **Large Dataset Processing**: No pagination or data limiting
3. **Multiple Chart Generation**: Sequential chart generation could be slow

### Performance Improvements

1. **Optimized Queries**: Use database-level aggregation where possible
2. **Selective Chart Generation**: Only generate charts when data is sufficient
3. **Async Processing**: Consider async chart generation for large datasets
4. **Cache Strategy**: Implement smarter cache invalidation

## Security Considerations

### Input Validation

1. **Parameter Sanitization**: Validate department_id and sector_id parameters
2. **SQL Injection Prevention**: Use parameterized queries (already implemented)
3. **Access Control**: Ensure users have permission to view competency data

### Data Privacy

1. **Employee Data Protection**: Ensure competency data is only accessible to authorized users
2. **Audit Logging**: Log access to sensitive competency reports
3. **Data Anonymization**: Consider anonymizing individual employee data in charts

## Deployment Considerations

### Rollback Strategy

1. **Database Compatibility**: Changes are backward compatible
2. **Service Availability**: Fix can be deployed without downtime
3. **Cache Clearing**: Clear competency-related cache after deployment

### Monitoring

1. **Error Rate Monitoring**: Monitor for competency report errors
2. **Performance Metrics**: Track chart generation times
3. **User Feedback**: Monitor user reports of issues

## Future Enhancements

### Potential Improvements

1. **Real-time Updates**: Implement WebSocket updates for live data
2. **Export Functionality**: Add PDF/Excel export for reports
3. **Advanced Filtering**: Add more granular filtering options
4. **Predictive Analytics**: Add trend analysis and forecasting
5. **Mobile Optimization**: Ensure charts render well on mobile devices