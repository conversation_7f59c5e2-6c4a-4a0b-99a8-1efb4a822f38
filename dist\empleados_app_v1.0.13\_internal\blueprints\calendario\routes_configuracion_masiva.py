# -*- coding: utf-8 -*-
from flask import jsonify, request
from . import calendario_bp
# from services.calendario_service import calendario_service
from datetime import datetime
import logging
import json

@calendario_bp.route('/calendario/<int:calendario_id>/configurar-masivo', methods=['POST'])
def configurar_masivo(calendario_id):
    """Configurar múltiples días en el calendario de forma masiva"""
    try:
        # Obtener datos del formulario
        fechas_json = request.form.get('fechas_seleccionadas')
        fechas = json.loads(fechas_json)

        es_laborable = request.form.get('masivo_es_laborable') == 'true'
        duracion_jornada = request.form.get('masivo_duracion_jornada', 8, type=int)
        notas = request.form.get('masivo_notas', '')

        # Configurar cada día
        exitos = 0
        errores = 0
        for fecha_str in fechas:
            try:
                fecha = datetime.strptime(fecha_str, "%Y-%m-%d").date()

                # Configurar el día
                # config = calendario_service.set_configuracion_dia(
                #     calendario_id=calendario_id,
                #     fecha=fecha,
                #     es_laborable=es_laborable,
                #     duracion_jornada=duracion_jornada,
                #     notas=notas
                # )

                if config:
                    # Procesar excepciones por turno
                    turnos_ids = request.form.getlist('masivo_turnos_ids[]')
                    for turno_id in turnos_ids:
                        turno_id = int(turno_id)
                        turno_es_laborable = request.form.get(f'masivo_turno_{turno_id}_laborable') == 'true'
                        turno_duracion = request.form.get(f'masivo_turno_{turno_id}_duracion', 8, type=int)

                        # calendario_service.set_excepcion_turno(
                        #     configuracion_id=config.id,
                        #     turno_id=turno_id,
                        #     es_laborable=turno_es_laborable,
                        #     duracion_jornada=turno_duracion
                        # )

                    exitos += 1
                else:
                    errores += 1
            except Exception as e:
                logging.error(f"Error al configurar día {fecha_str}: {str(e)}")
                errores += 1

        if errores == 0:
            return jsonify({"success": True, "message": f"Se configuraron correctamente {exitos} días"})
        elif exitos > 0:
            return jsonify({"success": True, "message": f"Se configuraron {exitos} días, pero hubo errores en {errores} días"})
        else:
            return jsonify({"success": False, "error": "No se pudo configurar ningún día"}), 500

    except Exception as e:
        logging.error(f"Error al configurar días masivamente: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500
