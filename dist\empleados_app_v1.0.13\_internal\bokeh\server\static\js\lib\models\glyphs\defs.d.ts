import type { <PERSON><PERSON><PERSON>, <PERSON>llVector, <PERSON><PERSON><PERSON> } from "../../core/visuals";
import type { Context2d } from "../../core/util/canvas";
export type VectorVisuals = {
    line: LineVector;
    fill: FillVector;
    hatch: HatchVector;
};
declare function asterisk(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function circle(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function circle_cross(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function circle_dot(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function circle_y(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function circle_x(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function cross(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function diamond(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function diamond_cross(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function diamond_dot(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function dot(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function hex(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function hex_dot(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function inverted_triangle(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function plus(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function square(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function square_pin(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function square_cross(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function square_dot(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function square_x(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function star(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function star_dot(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function triangle(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function triangle_dot(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function triangle_pin(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function dash(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function x(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
declare function y(ctx: Context2d, i: number, r: number, visuals: VectorVisuals): void;
export type RenderOne = (ctx: Context2d, i: number, r: number, visuals: VectorVisuals) => void;
export declare const marker_funcs: {
    asterisk: typeof asterisk;
    circle: typeof circle;
    circle_cross: typeof circle_cross;
    circle_dot: typeof circle_dot;
    circle_y: typeof circle_y;
    circle_x: typeof circle_x;
    cross: typeof cross;
    diamond: typeof diamond;
    diamond_dot: typeof diamond_dot;
    diamond_cross: typeof diamond_cross;
    dot: typeof dot;
    hex: typeof hex;
    hex_dot: typeof hex_dot;
    inverted_triangle: typeof inverted_triangle;
    plus: typeof plus;
    square: typeof square;
    square_cross: typeof square_cross;
    square_dot: typeof square_dot;
    square_pin: typeof square_pin;
    square_x: typeof square_x;
    star: typeof star;
    star_dot: typeof star_dot;
    triangle: typeof triangle;
    triangle_dot: typeof triangle_dot;
    triangle_pin: typeof triangle_pin;
    dash: typeof dash;
    x: typeof x;
    y: typeof y;
};
export {};
//# sourceMappingURL=defs.d.ts.map