# -*- coding: utf-8 -*-
from flask import render_template, request, redirect, url_for, flash
from . import evaluations_bp
from models import Empleado, db
from models_evaluacion import EvaluacionEmpleado, PlantillaEvaluacion
from sqlalchemy.orm import joinedload


@evaluations_bp.route('/')
def list_evaluations():
    """Listar evaluaciones usando el nuevo sistema rediseñado"""
    page = request.args.get('page', 1, type=int)
    per_page = 10
    
    # Obtener evaluaciones del nuevo sistema con la relación empleado cargada
    evaluations = EvaluacionEmpleado.query.options(joinedload(EvaluacionEmpleado.empleado)).order_by(EvaluacionEmpleado.fecha.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('evaluations/list.html',
                         evaluations=evaluations,
                         page=page,
                         per_page=per_page)

@evaluations_bp.route('/nueva', methods=['GET', 'POST'])
def new_evaluation():
    """Crear nueva evaluación usando el nuevo sistema rediseñado"""
    if request.method == 'POST':
        empleado_id = request.form.get('empleado_id')
        plantilla_id = request.form.get('plantilla_id')
        evaluador_id = request.form.get('evaluador_id')
        
        if empleado_id and plantilla_id and evaluador_id:
            # Redirigir al formulario de evaluación rediseñado
            return redirect(url_for('redesign_eval.evaluacion_formulario_redisenado', empleado_id=empleado_id))
        else:
            flash('Todos los campos son obligatorios', 'danger')
    
    # Obtener empleados activos y plantillas disponibles
    empleados = Empleado.query.filter_by(activo=True).all()
    plantillas = PlantillaEvaluacion.query.filter_by(activa=True).all()
    
    return render_template('evaluations/new.html', 
                         empleados=empleados, 
                         plantillas=plantillas)
