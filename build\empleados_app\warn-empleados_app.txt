
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named 'collections.abc' - imported by traceback (top-level), typing (top-level), inspect (top-level), logging (top-level), importlib.resources.readers (top-level), selectors (top-level), tracemalloc (top-level), http.client (top-level), werkzeug.datastructures.structures (top-level), markupsafe (top-level), typing_extensions (top-level), asyncio.base_events (top-level), asyncio.coroutines (top-level), werkzeug.datastructures.etag (top-level), watchdog.utils.patterns (conditional), watchdog.events (conditional), watchdog.observers.inotify_c (conditional), watchdog.utils.dirsnapshot (conditional), watchdog.observers.kqueue (conditional), watchdog.observers.polling (conditional), flask.app (top-level), importlib_metadata (top-level), importlib_metadata._meta (top-level), blinker.base (top-level), blinker._utilities (top-level), flask.sessions (top-level), itsdangerous.serializer (top-level), itsdangerous.signer (top-level), itsdangerous.timed (top-level), sqlalchemy.util.compat (conditional), sqlalchemy.util.typing (top-level), sqlalchemy.sql.coercions (top-level), sqlalchemy.sql.traversals (top-level), sqlalchemy.sql.compiler (top-level), sqlalchemy.sql.dml (top-level), sqlalchemy.sql.sqltypes (top-level), sqlalchemy.engine.row (top-level), sqlalchemy.sql.lambdas (top-level), sqlalchemy.engine.url (top-level), sqlalchemy.orm.query (top-level), numpy.lib._npyio_impl (top-level), numpy.lib._function_base_impl (top-level), numpy._typing._nested_sequence (conditional), numpy._typing._shape (top-level), numpy._typing._dtype_like (top-level), numpy._typing._array_like (top-level), yaml.constructor (top-level), numpy.random.bit_generator (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level), pandas._typing (top-level), pytz.lazy (optional), pandas.util._exceptions (conditional), pandas._config.config (conditional), pandas.util.version (top-level), pandas.core.dtypes.inference (conditional), scipy._lib._docscrape (top-level), scipy.stats._stats_py (top-level), matplotlib (top-level), matplotlib.cbook (top-level), matplotlib._path (top-level), matplotlib.colors (top-level), PIL.Image (top-level), PIL._typing (top-level), xml.etree.ElementTree (top-level), PIL.ImageFilter (top-level), PIL.ImagePalette (top-level), PIL.TiffImagePlugin (top-level), PIL.ImageOps (top-level), PIL.PngImagePlugin (top-level), pyparsing.core (top-level), pyparsing.results (top-level), cycler (top-level), matplotlib.cm (top-level), matplotlib.markers (top-level), matplotlib._mathtext (conditional), matplotlib.axes._base (top-level), matplotlib.spines (top-level), tornado.gen (top-level), tornado.httputil (top-level), matplotlib.pyplot (conditional), matplotlib.typing (top-level), scipy.spatial.distance (top-level), scipy.stats._qmc (top-level), scipy._lib.doccer (top-level), scipy.integrate._quadrature (top-level), scipy.stats._continuous_distns (top-level), scipy.stats._resampling (top-level), scipy.stats._multicomp (top-level), scipy.stats._sensitivity_analysis (top-level), scipy.ndimage._filters (top-level), scipy.ndimage._ni_support (top-level), scipy.linalg._decomp_cossin (top-level), requests.compat (top-level), pandas.core.frame (top-level), pandas.util._validators (top-level), pandas.core.construction (top-level), pandas.core.common (top-level), pandas.util._decorators (conditional), pandas.core.dtypes.concat (conditional), pandas.core.sorting (conditional), pandas.core.indexes.category (conditional), pyarrow.vendored.docscrape (top-level), pandas.core.arrays.masked (conditional), pandas.core.apply (conditional), pandas.core.base (conditional), pandas.core.groupby.groupby (top-level), pandas.core.arrays.numeric (conditional), pandas.core.strings.base (conditional), pandas.core.strings.object_array (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.groupby.base (conditional), pandas.core.series (top-level), pandas.io.formats.format (top-level), pandas.core.arrays.timedeltas (conditional), pandas.core.indexes.range (top-level), pandas.core.tools.timedeltas (conditional), pandas.core.indexes.datetimelike (conditional), pandas.core.reshape.concat (conditional), pandas.io.common (top-level), pandas.io.formats.printing (top-level), pandas.core.indexes.multi (top-level), pandas.io.formats.html (conditional), pandas.io.formats.string (conditional), pandas.io.formats.csvs (top-level), pandas.io.formats.style_render (top-level), pandas.core.arrays.interval (conditional), pandas.core.indexes.interval (conditional), pandas.core.arrays.period (conditional), pandas.core.indexes.period (conditional), pandas.core.indexing (conditional), pandas.core.tools.datetimes (conditional), pandas.core.interchange.dataframe_protocol (conditional), pandas.core.window.rolling (conditional), pandas.io.json._normalize (conditional), pandas.io.parsers.base_parser (conditional), pandas.io.parsers.c_parser_wrapper (conditional), pandas.io.parsers.python_parser (top-level), pandas.io.parsers.readers (conditional), pandas.io.json._json (conditional), pandas.io.stata (conditional), pandas.io.formats.style (conditional), pandas.io.formats.excel (top-level), pandas.io.formats.css (conditional), pandas.io.excel._base (top-level), pandas.io.excel._util (top-level), pandas.core.arrays.sparse.array (conditional), pandas.core.arrays.sparse.scipy_sparse (conditional), pandas.core.methods.selectn (top-level), pandas.core.strings.accessor (conditional), pandas.io.formats.info (conditional), pandas.plotting._core (conditional), pandas.plotting._misc (conditional), pandas.core.groupby.grouper (conditional), pandas.core.groupby.ops (conditional), pandas.core.groupby.indexing (top-level), pandas.core.internals.blocks (conditional), pandas.core.resample (conditional), pandas.core.groupby.generic (conditional), pandas.core.reshape.merge (top-level), pandas.core.arrays.arrow.array (conditional), pandas.core.arrays.datetimelike (conditional), pandas.core.arrays.datetimes (conditional), pandas.core.indexes.datetimes (conditional), pandas.core.arrays._mixins (conditional), pandas.core.arrays.categorical (conditional), pandas.core.internals.managers (top-level), pandas.core.internals.ops (conditional), pandas.core.internals.array_manager (conditional), pandas.core.internals.construction (conditional), pandas.core.methods.describe (conditional), pandas.core.generic (conditional), pandas.core.computation.parsing (conditional), pandas.compat.pickle_compat (conditional), pandas.core.computation.ops (conditional), pandas.core.computation.align (conditional), pandas.io.pytables (conditional), pandas.io.sql (conditional), sqlite3.dbapi2 (top-level), pandas.core.arrays.base (conditional), pandas.core.internals.concat (conditional), pandas.core.indexes.base (conditional), pandas.core.dtypes.cast (conditional), pandas.core.reshape.melt (conditional), pandas.core.interchange.dataframe (conditional), pandas.io.feather_format (conditional), pyarrow.feather (top-level), pyarrow.pandas_compat (top-level), pandas.io.xml (conditional), pandas.core.reshape.pivot (top-level), fsspec.utils (top-level), configparser (top-level), fsspec.dircache (top-level), fsspec.asyn (top-level), fsspec.implementations.cache_metadata (conditional), fsspec.json (top-level), fsspec.mapping (top-level), pandas.core.arrays.arrow.accessors (conditional), pandas.core.dtypes.dtypes (conditional), pandas.core.util.hashing (conditional), pandas.core.reshape.encoding (top-level), pandas._config.localization (conditional), pandas._testing.contexts (conditional), pandas._testing._warnings (conditional), pandas.io.html (conditional), pandas.io.sas.sasreader (conditional), pandas.io.spss (conditional), anyio._core._eventloop (top-level), anyio.abc._eventloop (top-level), anyio._core._exceptions (top-level), anyio._core._tasks (top-level), anyio.abc._tasks (top-level), anyio._core._testing (top-level), anyio.from_thread (top-level), anyio.abc._sockets (top-level), anyio._core._typedattr (top-level), anyio.abc._streams (top-level), anyio._core._sockets (top-level), anyio.to_thread (top-level), anyio.streams.stapled (top-level), anyio.streams.tls (top-level), anyio.abc._testing (top-level), anyio._core._fileio (top-level), anyio._core._signals (top-level), anyio._core._subprocesses (top-level), anyio._core._tempfile (top-level), httpx._models (top-level), markdown_it.main (top-level), markdown_it.ruler (top-level), markdown_it.utils (top-level), markdown_it.token (top-level), markdown_it.common.normalize_url (top-level), mdurl._decode (top-level), mdurl._encode (top-level), markdown_it._punycode (top-level), markdown_it.parser_block (top-level), markdown_it.parser_core (top-level), markdown_it.parser_inline (top-level), markdown_it.renderer (top-level), rich.scope (top-level), attr._compat (top-level), attr._make (top-level), dns.immutable (top-level), babel.core (top-level), babel.localedata (top-level), babel.plural (top-level), babel.support (top-level), babel.dates (top-level), alembic.util.langhelpers (top-level), alembic.util.messaging (top-level), sqlalchemy.dialects.postgresql.psycopg2 (top-level), narwhals._compliant.dataframe (top-level), narwhals._compliant.typing (top-level), narwhals._compliant.expr (top-level), narwhals._utils (top-level), narwhals.exceptions (conditional), narwhals._translate (top-level), narwhals._expression_parsing (conditional), narwhals.expr (top-level), narwhals.dtypes (top-level), narwhals.typing (conditional), narwhals.series (top-level), narwhals._arrow.utils (conditional), narwhals._arrow.expr (conditional), narwhals._arrow.dataframe (top-level), narwhals._arrow.group_by (conditional), narwhals._arrow.namespace (conditional), narwhals.schema (conditional), narwhals._pandas_like.utils (conditional), narwhals._duration (conditional), narwhals._pandas_like.group_by (conditional), narwhals._pandas_like.dataframe (top-level), narwhals._pandas_like.series (conditional), narwhals._pandas_like.namespace (conditional), narwhals._polars.dataframe (top-level), narwhals._polars.utils (conditional), narwhals._polars.series (conditional), narwhals._polars.expr (conditional), narwhals._polars.namespace (conditional), narwhals._polars.group_by (conditional), narwhals._duckdb.utils (conditional), narwhals._duckdb.expr_dt (conditional), narwhals._duckdb.expr (conditional), narwhals._duckdb.namespace (conditional), narwhals._duckdb.typing (conditional), narwhals._duckdb.dataframe (conditional), narwhals._duckdb.group_by (conditional), narwhals.functions (top-level), narwhals.selectors (conditional), narwhals.stable.v1 (conditional), narwhals._dask.utils (conditional), narwhals._dask.expr (conditional), narwhals._dask.namespace (conditional), narwhals._dask.dataframe (conditional), narwhals._dask.group_by (conditional), narwhals._ibis.utils (conditional), narwhals._ibis.expr (conditional), narwhals._ibis.namespace (conditional), narwhals._ibis.dataframe (conditional), narwhals._ibis.group_by (conditional), narwhals._pandas_like.expr (conditional), narwhals._arrow.series_dt (conditional), narwhals._arrow.series (conditional), narwhals.dataframe (conditional), narwhals.group_by (conditional), narwhals._compliant.namespace (conditional), narwhals._compliant.selectors (conditional), narwhals._compliant.when_then (conditional), narwhals._compliant.window (conditional), narwhals._compliant.series (conditional), narwhals._compliant.group_by (conditional), narwhals._namespace (conditional), narwhals._spark_like.expr_dt (conditional), narwhals._spark_like.expr (conditional), narwhals._spark_like.namespace (conditional), narwhals._spark_like.dataframe (conditional), narwhals._spark_like.group_by (conditional), bokeh.core.property.container (top-level), bokeh.plotting._renderer (top-level), bokeh.plotting._plot (top-level), sklearn.utils (top-level), sklearn.utils._param_validation (top-level), sklearn.utils.validation (top-level), sklearn.utils.multiclass (top-level), sklearn.model_selection._classification_threshold (top-level), sklearn.model_selection._split (top-level), sklearn.model_selection._search (top-level), sklearn.utils._testing (top-level), anyio._backends._asyncio (top-level), anyio._core._asyncio_selector_thread (top-level), anyio._backends._trio (top-level), fsspec.gui (top-level), sklearn.tree._export (top-level), pandas.plotting._matplotlib.core (top-level), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.converter (conditional), pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.misc (conditional), pandas.plotting._matplotlib.groupby (conditional), pandas.plotting._matplotlib.boxplot (conditional), PIL.Jpeg2KImagePlugin (top-level), PIL.IptcImagePlugin (top-level), pkg_resources._vendor.more_itertools.more (top-level), pkg_resources._vendor.more_itertools.recipes (top-level), pkg_resources._vendor.typing_extensions (top-level), PIL.ImageDraw (top-level), scipy._lib.array_api_compat.common._fft (conditional), scipy.constants._codata (top-level), sqlalchemy.ext.baked (top-level)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), http.server (delayed, optional), netrc (delayed, optional), getpass (delayed, optional)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level), fsspec.asyn (conditional, optional)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed), joblib.externals.loky.backend.fork_exec (delayed)
missing module named fcntl - imported by subprocess (optional), tqdm.utils (delayed, optional), pty (delayed, optional), alembic.util.messaging (optional)
missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), pkg_resources._vendor.packaging._manylinux (delayed, optional)
missing module named jnius - imported by pkg_resources._vendor.platformdirs.android (delayed, optional)
missing module named platformdirs - imported by pkg_resources._vendor.platformdirs.__main__ (top-level)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level), joblib.externals.loky.backend.context (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level), joblib.parallel (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional), tty (top-level), _pyrepl.pager (delayed, optional), werkzeug._reloader (delayed, optional), click._termui_impl (conditional), tqdm.utils (delayed, optional), alembic.util.messaging (optional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named multiprocessing.Pool - imported by multiprocessing (delayed, conditional), scipy._lib._util (delayed, conditional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named annotationlib - imported by typing_extensions (conditional), sqlalchemy.util.langhelpers (conditional), attr._compat (conditional)
missing module named 'pkg_resources.extern.importlib_resources' - imported by pkg_resources._vendor.jaraco.text (optional)
missing module named 'pkg_resources.extern.more_itertools' - imported by pkg_resources._vendor.jaraco.functools (top-level)
missing module named pkg_resources.extern.packaging - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named pkg_resources.extern.platformdirs - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named 'pkg_resources.extern.jaraco' - imported by pkg_resources (top-level), pkg_resources._vendor.jaraco.text (top-level)
missing module named imp - imported by pkg_resources (optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named readline - imported by code (delayed, conditional, optional), flask.cli (delayed, conditional, optional), rlcompleter (optional), cmd (delayed, conditional, optional), pdb (delayed, optional), sqlite3.__main__ (delayed, conditional, optional)
missing module named cryptography - imported by werkzeug.serving (delayed, conditional, optional), flask.cli (delayed, conditional, optional), urllib3.contrib.pyopenssl (top-level), requests (conditional, optional)
missing module named 'IPython.core' - imported by dotenv.ipython (top-level), pandas.io.formats.printing (delayed, conditional), rich.pretty (delayed, optional)
missing module named IPython - imported by dotenv.ipython (top-level), pandas.io.formats.printing (delayed), bokeh.io.notebook (delayed, optional)
missing module named _watchdog_fsevents - imported by watchdog.observers.fsevents (top-level)
missing module named 'cryptography.hazmat' - imported by werkzeug.serving (delayed, optional)
missing module named 'cryptography.x509' - imported by werkzeug.serving (delayed, conditional, optional), urllib3.contrib.pyopenssl (delayed, optional)
missing module named '_typeshed.wsgi' - imported by werkzeug.exceptions (conditional), werkzeug.http (conditional), werkzeug.wsgi (conditional), werkzeug.utils (conditional), werkzeug.wrappers.response (conditional), werkzeug.test (conditional), werkzeug.formparser (conditional), werkzeug.wrappers.request (conditional), werkzeug.serving (conditional), werkzeug.debug (conditional), werkzeug.middleware.shared_data (conditional), werkzeug.local (conditional), werkzeug.routing.exceptions (conditional), werkzeug.routing.map (conditional), flask.typing (conditional), httpx._transports.wsgi (conditional)
missing module named _typeshed - imported by werkzeug._internal (conditional), asgiref.sync (conditional), numpy.random.bit_generator (top-level), anyio.abc._eventloop (conditional), anyio._core._sockets (conditional), anyio._core._fileio (conditional), anyio._core._tempfile (conditional), httpx._transports.wsgi (conditional), anyio._backends._asyncio (conditional), anyio._core._asyncio_selector_thread (conditional), anyio._backends._trio (conditional)
missing module named wmi - imported by dns.win32util (conditional)
missing module named trio - imported by dns._trio_backend (top-level), httpcore._synchronization (optional), httpcore._backends.trio (top-level), httpx._transports.asgi (delayed, conditional), dns.quic (conditional), dns.quic._trio (top-level)
missing module named 'trio.testing' - imported by anyio._backends._trio (delayed)
missing module named exceptiongroup - imported by anyio._core._exceptions (conditional), anyio._core._sockets (conditional), anyio._backends._asyncio (conditional), anyio._backends._trio (conditional)
missing module named 'trio.to_thread' - imported by anyio._backends._trio (top-level)
missing module named 'trio.socket' - imported by dns._trio_backend (top-level), anyio._backends._trio (top-level)
missing module named outcome - imported by anyio._backends._trio (top-level)
missing module named 'trio.lowlevel' - imported by anyio._backends._trio (top-level)
missing module named 'trio.from_thread' - imported by anyio._backends._trio (top-level)
missing module named _pytest - imported by anyio._backends._asyncio (delayed)
missing module named uvloop - imported by anyio._backends._asyncio (delayed, conditional)
missing module named curio - imported by sniffio._impl (delayed, conditional)
missing module named socksio - imported by httpcore._sync.socks_proxy (top-level), httpcore._async.socks_proxy (top-level), httpx._transports.default (delayed, conditional, optional)
missing module named 'h2.settings' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.exceptions' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level), httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level), httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.config' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'IPython.display' - imported by tqdm.notebook (conditional, optional), rich.jupyter (delayed, optional), rich.live (delayed, conditional, optional), bokeh.io.notebook (delayed)
missing module named pygments.lexers.PrologLexer - imported by pygments.lexers (top-level), pygments.lexers.cplint (top-level)
missing module named _winreg - imported by pygments.formatters.img (optional)
missing module named numpy.byte_bounds - imported by numpy (delayed, optional), joblib._memmapping_reducer (delayed, optional)
missing module named numpy.VisibleDeprecationWarning - imported by numpy (conditional), scipy._lib._util (conditional), matplotlib.cbook (optional), sklearn.utils.fixes (conditional)
missing module named numpy.ComplexWarning - imported by numpy (conditional), scipy._lib._util (conditional), sklearn.utils.fixes (conditional)
missing module named numpy.AxisError - imported by numpy (conditional), scipy._lib._util (conditional)
missing module named _dummy_thread - imported by numpy._core.arrayprint (optional)
missing module named 'numpy_distutils.cpuinfo' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.fcompiler' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.command' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named numpy_distutils - imported by numpy.f2py.diagnose (delayed, optional)
missing module named psutil - imported by numpy.testing._private.utils (delayed, optional), scipy._lib._testutils (delayed, optional), joblib.externals.loky.backend.context (delayed, optional), joblib.externals.loky.backend.utils (optional), joblib.externals.loky.process_executor (optional), pyarrow.tests.util (delayed)
missing module named numpy.random.RandomState - imported by numpy.random (top-level), numpy.random._generator (top-level)
missing module named pyodide_js - imported by threadpoolctl (delayed, optional)
missing module named numpy._core.zeros - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.vstack - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.void - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecmat - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecdot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ushort - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.unsignedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulonglong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ubyte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.trunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.true_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.transpose - imported by numpy._core (top-level), numpy.lib._function_base_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.trace - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.timedelta64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tensordot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.tanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.swapaxes - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sum - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.subtract - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.str_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.square - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sqrt - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.spacing - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.single - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.signedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.signbit - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.sign - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.short - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.right_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.result_type - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.remainder - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.reciprocal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.radians - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rad2deg - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.prod - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.positive - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.pi - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.outer - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ones - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.object_ - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.number - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.not_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.newaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.negative - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ndarray - imported by numpy._core (top-level), numpy.lib._utils_impl (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.multiply - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.moveaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.modf - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.mod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.minimum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.maximum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.max - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.matrix_transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matvec - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.matmul - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.longdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.long - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_not - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log1p - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.linspace - imported by numpy._core (top-level), numpy.lib._index_tricks_impl (top-level), numpy (conditional)
missing module named numpy._core.less_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.left_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ldexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.lcm - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.isscalar - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isnat - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.isnan - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isfinite - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intp - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.integer - imported by numpy._core (conditional), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.intc - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.int8 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.inf - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.inexact - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.iinfo - imported by numpy._core (top-level), numpy.lib._twodim_base_impl (top-level), numpy (conditional)
missing module named numpy._core.hypot - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.hstack - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.heaviside - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.half - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.gcd - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frompyfunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmax - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floating - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float_power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float32 - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.float16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.finfo - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.fabs - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.expm1 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.exp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.euler_gamma - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.errstate - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.empty_like - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.empty - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.e - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.double - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.dot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.divmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.divide - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.diagonal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.degrees - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.deg2rad - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.datetime64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.csingle - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cross - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.count_nonzero - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.copysign - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.conjugate - imported by numpy._core (conditional), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.conj - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.complexfloating - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.complex64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.clongdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.character - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ceil - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cdouble - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cbrt - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bytes_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.byte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bool_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_count - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.atleast_3d - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_2d - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.atleast_1d - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.asarray - imported by numpy._core (top-level), numpy.lib._array_utils_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level), numpy.fft._helper (top-level)
missing module named numpy._core.asanyarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.array_repr - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.array2string - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.array - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.argsort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.arctanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arange - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.amin - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amax - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.all - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.add - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named defusedxml - imported by PIL.Image (optional), openpyxl.xml (delayed, optional)
missing module named ctags - imported by pygments.formatters.html (optional)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional), rich.live (delayed, conditional, optional)
missing module named linkify_it - imported by markdown_it.main (optional)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional), fsspec.compression (optional), httpx._decoders (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional), httpx._decoders (optional)
missing module named h2 - imported by urllib3.http2.connection (top-level), httpx._client (delayed, conditional, optional)
missing module named 'aioquic.quic' - imported by dns.quic._asyncio (top-level), dns.quic._common (top-level), dns.quic._sync (top-level), dns.quic._trio (top-level)
missing module named 'aioquic.h3' - imported by dns.quic._common (top-level)
missing module named aioquic - imported by dns.quic (conditional)
missing module named sets - imported by pytz.tzinfo (optional), reportlab.platypus.doctemplate (optional)
missing module named collections.Mapping - imported by collections (optional), pytz.lazy (optional)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named 'defusedxml.ElementTree' - imported by openpyxl.xml.functions (conditional)
missing module named 'lxml.etree' - imported by openpyxl.xml.functions (conditional), pandas.io.xml (delayed), pandas.io.formats.xml (delayed), pandas.io.html (delayed)
missing module named openpyxl.tests - imported by openpyxl.reader.excel (optional)
missing module named lxml - imported by openpyxl.xml (delayed, optional), pandas.io.xml (conditional)
missing module named pysqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed)
missing module named sqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed, optional)
missing module named 'psycopg.pq' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed)
missing module named 'psycopg.types' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named 'psycopg.adapt' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named psycopg - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named asyncpg - imported by sqlalchemy.dialects.postgresql.asyncpg (delayed)
missing module named oracledb - imported by sqlalchemy.dialects.oracle.oracledb (delayed, conditional)
missing module named cx_Oracle - imported by sqlalchemy.dialects.oracle.cx_oracle (delayed)
missing module named 'mysql.connector' - imported by sqlalchemy.dialects.mysql.mysqlconnector (delayed, conditional, optional)
missing module named mysql - imported by sqlalchemy.dialects.mysql.mysqlconnector (delayed)
missing module named asyncmy - imported by sqlalchemy.dialects.mysql.asyncmy (delayed)
missing module named pymysql - imported by sqlalchemy.dialects.mysql.aiomysql (delayed)
missing module named psycopg2 - imported by sqlalchemy.dialects.postgresql.psycopg2 (delayed), sqlalchemy (top-level)
missing module named reportlab_mods - imported by reportlab (optional)
missing module named 'reportlab.local_rl_mods' - imported by reportlab (optional)
missing module named 'reportlab.lib.pyHnj' - imported by reportlab.lib.utils (delayed, optional)
missing module named macostools - imported by reportlab.lib.utils (conditional), reportlab.graphics.renderPDF (delayed, conditional, optional), reportlab.graphics.shapes (delayed, conditional)
missing module named macfs - imported by reportlab.lib.utils (conditional), reportlab.graphics.renderPDF (delayed, conditional, optional), reportlab.graphics.shapes (delayed, conditional)
missing module named rlextra - imported by reportlab.lib.pdfencrypt (delayed, optional)
missing module named pyaes - imported by reportlab.lib.pdfencrypt (optional)
missing module named pyfribidi2 - imported by reportlab.pdfgen.textobject (conditional, optional)
missing module named 'tests.test_platypus_tables' - imported by reportlab.platypus.tables (conditional)
missing module named renderPM - imported by reportlab.graphics.charts.utils (delayed, conditional)
missing module named freetype - imported by reportlab.graphics.utils (delayed, conditional, optional)
missing module named _rl_renderPM - imported by reportlab.graphics.utils (delayed, conditional, optional), reportlab.graphics.renderPM (delayed, conditional, optional)
missing module named 'rlextra.graphics' - imported by reportlab.graphics.charts.textlabels (optional), reportlab.graphics.shapes (delayed, conditional, optional)
missing module named Image - imported by reportlab.graphics.renderPM (delayed, optional)
missing module named rlPyCairo - imported by reportlab.graphics.renderPM (delayed, conditional, optional)
missing module named new - imported by reportlab.lib.attrmap (delayed, conditional)
missing module named reportlab.platypus.XPreformatted - imported by reportlab.platypus (top-level), reportlab.graphics.charts.textlabels (top-level)
missing module named pyphen - imported by reportlab.platypus.paragraph (optional)
missing module named reportlab.platypus.cleanBlockQuotedText - imported by reportlab.platypus (conditional), reportlab.platypus.paraparser (conditional)
missing module named flask_cache - imported by cache (optional)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named StringIO - imported by six (conditional)
missing module named pytest - imported by scipy._lib._testutils (delayed), pandas._testing._io (delayed), pandas._testing (delayed), sklearn.utils._testing (optional), fsspec.conftest (top-level), pyarrow.conftest (top-level), pyarrow.tests.util (top-level)
missing module named cupy_backends - imported by scipy._lib.array_api_compat.common._helpers (delayed)
missing module named 'cupy.cuda' - imported by scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib.array_api_compat.common._helpers (delayed)
missing module named 'jax.experimental' - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named 'jax.numpy' - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named 'dask.array' - imported by scipy._lib.array_api_compat.dask.array (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional), narwhals._dask.expr (delayed)
missing module named sparse - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy.sparse.linalg._expm_multiply (delayed, conditional), scipy.sparse.linalg._matfuncs (delayed, conditional)
missing module named dask - imported by scipy._lib.array_api_compat.common._helpers (delayed), narwhals._utils (delayed, conditional), joblib._dask (optional), fsspec.implementations.dask (top-level)
missing module named ndonnx - imported by scipy._lib.array_api_compat.common._helpers (delayed)
missing module named torch - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_compat.torch (top-level), scipy._lib.array_api_compat.torch._info (top-level), scipy._lib.array_api_compat.torch._aliases (top-level), scipy._lib._array_api (delayed, conditional)
missing module named cupy - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_compat.cupy (top-level), scipy._lib.array_api_compat.cupy._aliases (top-level), scipy._lib.array_api_compat.cupy._info (top-level), scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib._array_api (delayed, conditional), narwhals._pandas_like.utils (delayed, conditional), sklearn.utils._testing (delayed, conditional)
missing module named jax - imported by scipy._lib.array_api_compat.common._helpers (delayed), scipy._lib._array_api (delayed, conditional)
missing module named Cython - imported by scipy._lib._testutils (optional)
missing module named cython - imported by scipy._lib._testutils (optional), pyarrow.conftest (optional)
missing module named sphinx - imported by scipy._lib._docscrape (delayed, conditional)
missing module named cupyx - imported by scipy._lib._array_api (delayed, conditional)
missing module named scipy.sparse.issparse - imported by scipy.sparse (delayed), scipy._lib._array_api (delayed), scipy.sparse.linalg._interface (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._constraints (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._lsq.least_squares (top-level), scipy.optimize._lsq.common (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._differentialevolution (top-level), scipy.sparse.csgraph._laplacian (top-level), scipy.optimize._milp (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.sparse.linalg._norm (top-level), pandas.core.dtypes.common (delayed, conditional, optional), sklearn.utils._param_validation (top-level), sklearn.externals._scipy.sparse.csgraph._laplacian (top-level), sklearn.utils._set_output (top-level), sklearn.utils.multiclass (top-level), sklearn.metrics.cluster._unsupervised (top-level), sklearn.metrics.pairwise (top-level), sklearn.metrics._pairwise_distances_reduction._dispatcher (top-level), sklearn.cluster._feature_agglomeration (top-level), sklearn.cluster._bicluster (top-level), sklearn.neighbors._base (top-level), sklearn.decomposition._pca (top-level), sklearn.cluster._hdbscan.hdbscan (top-level), sklearn.cluster._optics (top-level), sklearn.manifold._isomap (top-level), sklearn.manifold._t_sne (top-level), sklearn.metrics._classification (top-level), sklearn.metrics._ranking (top-level), sklearn.utils._indexing (top-level), sklearn.tree._classes (top-level), scipy.sparse.csgraph._validation (top-level)
missing module named scipy.sparse.linalg.LinearOperator - imported by scipy.sparse.linalg (top-level), scipy.optimize._optimize (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._differentiable_functions (top-level), scipy.optimize._trustregion_constr.minimize_trustregion_constr (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.tr_interior_point (top-level), scipy.optimize._lbfgsb_py (top-level), scipy.optimize._lsq.least_squares (top-level), scipy.optimize._lsq.common (top-level), scipy.optimize._lsq.dogbox (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.linalg.interpolative (delayed), scipy.sparse.csgraph._laplacian (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level), sklearn.externals._scipy.sparse.csgraph._laplacian (top-level), sklearn.utils.sparsefuncs (top-level)
missing module named scipy.linalg._fblas_64 - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._cblas - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._flapack_64 - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._clapack - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg.qr_insert - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named scipy.special.inv_boxcox - imported by scipy.special (top-level), sklearn.preprocessing._data (top-level)
missing module named scipy.special.boxcox - imported by scipy.special (top-level), sklearn.preprocessing._data (top-level)
missing module named scipy.special.gammaincinv - imported by scipy.special (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.ive - imported by scipy.special (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.betaln - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.beta - imported by scipy.special (top-level), scipy.stats._tukeylambda_stats (top-level)
missing module named scipy.special.loggamma - imported by scipy.special (top-level), scipy.fft._fftlog_backend (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.interpolate.PPoly - imported by scipy.interpolate (top-level), scipy.interpolate._cubic (top-level), scipy.spatial.transform._rotation_spline (delayed), scipy.integrate._bvp (delayed)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level)
missing module named railroad - imported by pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed)
missing module named gi - imported by matplotlib.cbook (delayed, conditional)
missing module named setuptools_scm - imported by matplotlib (delayed, conditional, optional), pyarrow (optional), tqdm.version (optional)
missing module named scikits - imported by scipy.optimize._linprog_ip (optional)
missing module named 'sksparse.cholmod' - imported by scipy.optimize._linprog_ip (optional)
missing module named sksparse - imported by scipy.optimize._trustregion_constr.projections (optional), scipy.optimize._linprog_ip (optional)
missing module named scipy.special.airy - imported by scipy.special (top-level), scipy.special._orthogonal (top-level)
missing module named scipy.linalg.orthogonal_procrustes - imported by scipy.linalg (top-level), scipy.spatial._procrustes (top-level)
missing module named uarray - imported by scipy._lib.uarray (conditional, optional)
missing module named 'scikits.umfpack' - imported by scipy.sparse.linalg._dsolve.linsolve (optional)
missing module named scipy.sparse.linalg.splu - imported by scipy.sparse.linalg (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level)
missing module named scipy.sparse.linalg.aslinearoperator - imported by scipy.sparse.linalg (top-level), scipy.optimize._lsq.common (top-level), scipy.optimize._lsq.dogbox (top-level), scipy.linalg.interpolative (delayed), scipy.sparse.linalg._svdp (top-level), scipy.sparse.linalg._expm_multiply (top-level), scipy.sparse.linalg._onenormest (top-level)
missing module named scipy.sparse.linalg.lsmr - imported by scipy.sparse.linalg (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._lsq.dogbox (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._lsq.trf_linear (top-level)
missing module named scipy.sparse.linalg.onenormest - imported by scipy.sparse.linalg (top-level), scipy.linalg._matfuncs_inv_ssq (top-level)
missing module named scipy.sparse.diags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.spdiags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.dia_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.kron - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.diags_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.eye_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.SparseEfficiencyWarning - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), sklearn.cluster._optics (top-level)
missing module named scipy.sparse.csr_array - imported by scipy.sparse (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._ndbspline (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), sklearn.utils.fixes (optional)
missing module named scipy.sparse.csc_array - imported by scipy.sparse (top-level), scipy.optimize._milp (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.vstack - imported by scipy.sparse (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._milp (top-level)
missing module named scipy.sparse.bmat - imported by scipy.sparse (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.qp_subproblem (top-level)
missing module named scipy.sparse.eye - imported by scipy.sparse (top-level), scipy.optimize._trustregion_constr.equality_constrained_sqp (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level), sklearn.manifold._locally_linear (top-level)
missing module named scipy.sparse.find - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.integrate._ivp.common (top-level)
missing module named scipy.sparse.coo_matrix - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.common (top-level), scipy.stats._crosstab (top-level), pandas.core.arrays.sparse.accessor (delayed), sklearn.metrics._classification (top-level)
missing module named scipy.sparse.csr_matrix - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._lsq.lsq_linear (top-level), sklearn.utils._param_validation (top-level), sklearn.metrics.pairwise (top-level), sklearn.neighbors._base (top-level), sklearn.manifold._locally_linear (top-level), sklearn.manifold._t_sne (top-level), sklearn.metrics._classification (top-level), sklearn.metrics._ranking (top-level)
missing module named scipy.sparse.csc_matrix - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.qp_subproblem (top-level), scipy.optimize._linprog_highs (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.linalg._sketches (top-level), sklearn.cluster._spectral (top-level)
missing module named scipy.stats.iqr - imported by scipy.stats (delayed), scipy.stats._hypotests (delayed)
missing module named cffi - imported by scipy._lib._ccallback (delayed, optional), pyarrow.cffi (top-level)
missing module named scipy._distributor_init_local - imported by scipy (optional), scipy._distributor_init (optional)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed)
missing module named pandas.core.groupby.PanelGroupBy - imported by pandas.core.groupby (delayed, optional), tqdm.std (delayed, optional)
missing module named numba - imported by pandas.core._numba.executor (delayed, conditional), pandas.core.util.numba_ (delayed, conditional), pandas.core.groupby.numba_ (delayed, conditional), pandas.core.window.numba_ (delayed, conditional), pandas.core.window.online (delayed, conditional), pandas.core._numba.kernels.mean_ (top-level), pandas.core._numba.kernels.shared (top-level), pandas.core._numba.kernels.sum_ (top-level), pandas.core._numba.kernels.min_max_ (top-level), pandas.core._numba.kernels.var_ (top-level), pandas.core._numba.extensions (top-level)
missing module named 'numba.extending' - imported by pandas.core._numba.kernels.sum_ (top-level)
missing module named pandas.core.window._Rolling_and_Expanding - imported by pandas.core.window (delayed, optional), tqdm.std (delayed, optional)
missing module named 'sphinx.ext' - imported by pyarrow.vendored.docscrape (delayed, conditional)
missing module named 'numba.typed' - imported by pandas.core._numba.extensions (delayed)
missing module named 'numba.core' - imported by pandas.core._numba.extensions (top-level)
missing module named 'pandas.api.internals' - imported by pyarrow.pandas_compat (delayed, conditional)
missing module named 'pyarrow._cuda' - imported by pyarrow.cuda (top-level)
missing module named 'pyarrow.gandiva' - imported by pyarrow.conftest (optional)
missing module named fastparquet - imported by fsspec.parquet (delayed), pyarrow.conftest (optional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named dummy_threading - imported by requests.cookies (optional), joblib.compressor (optional)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level), fsspec.implementations.http_sync (delayed, optional)
missing module named distributed - imported by fsspec.transaction (delayed), joblib._dask (optional), joblib._parallel_backends (delayed, optional)
missing module named requests_kerberos - imported by fsspec.implementations.webhdfs (delayed, conditional)
missing module named smbprotocol - imported by fsspec.implementations.smb (top-level)
missing module named smbclient - imported by fsspec.implementations.smb (top-level)
missing module named paramiko - imported by fsspec.implementations.sftp (top-level)
missing module named kerchunk - imported by fsspec.implementations.reference (delayed)
missing module named ujson - imported by fsspec.implementations.cache_metadata (optional), fsspec.implementations.reference (optional)
missing module named 'libarchive.ffi' - imported by fsspec.implementations.libarchive (top-level)
missing module named libarchive - imported by fsspec.implementations.libarchive (top-level)
missing module named yarl - imported by fsspec.implementations.http (top-level), fsspec.implementations.http_sync (optional)
missing module named aiohttp - imported by fsspec.implementations.http (top-level)
missing module named pygit2 - imported by fsspec.implementations.git (top-level)
missing module named 'distributed.worker' - imported by fsspec.implementations.dask (top-level)
missing module named 'distributed.client' - imported by fsspec.implementations.dask (top-level)
missing module named panel - imported by fsspec.gui (top-level)
missing module named fuse - imported by fsspec.fuse (top-level)
missing module named compression - imported by fsspec.compression (optional)
missing module named lz4 - imported by fsspec.compression (optional), joblib.compressor (optional)
missing module named snappy - imported by fsspec.compression (delayed, optional)
missing module named lzmaffi - imported by fsspec.compression (optional)
missing module named isal - imported by fsspec.compression (optional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named importlib_resources - imported by tqdm.cli (delayed, conditional, optional), alembic.util.compat (conditional)
missing module named 'pyarrow._azurefs' - imported by pyarrow.fs (optional)
missing module named 'setuptools_scm.git' - imported by pyarrow (delayed, optional)
missing module named traitlets - imported by pandas.io.formats.printing (delayed, conditional)
missing module named botocore - imported by pandas.io.common (delayed, conditional, optional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named cStringIO - imported by xlrd.timemachine (conditional)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed, conditional)
missing module named 'odf.office' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (conditional)
missing module named python_calamine - imported by pandas.io.excel._calamine (delayed, conditional)
missing module named pandas.core.internals.Block - imported by pandas.core.internals (conditional), pandas.io.pytables (conditional)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named qtpy - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named 'google.auth' - imported by pandas.io.gbq (conditional)
missing module named 'lxml.html' - imported by pandas.io.html (delayed)
missing module named bs4 - imported by pandas.io.html (delayed)
missing module named pandas.Panel - imported by pandas (delayed, optional), tqdm.std (delayed, optional)
missing module named array_api_compat - imported by sklearn.utils._array_api (delayed, conditional, optional), sklearn.utils._testing (delayed, optional)
missing module named 'distributed.utils' - imported by joblib._dask (conditional, optional)
missing module named 'dask.utils' - imported by joblib._dask (conditional)
missing module named 'dask.sizeof' - imported by joblib._dask (conditional)
missing module named 'dask.distributed' - imported by joblib._dask (conditional)
missing module named viztracer - imported by joblib.externals.loky.initializers (delayed, optional)
missing module named 'lz4.frame' - imported by joblib.compressor (optional)
missing module named 'numpydoc.docscrape' - imported by sklearn.utils._testing (delayed)
missing module named numpydoc - imported by sklearn.utils._testing (delayed, optional)
missing module named pyamg - imported by sklearn.manifold._spectral_embedding (delayed, conditional, optional)
missing module named weasyprint - imported by services.report_service (delayed, optional)
missing module named bokeh.models.renderers.DataRenderer - imported by bokeh.models.renderers (top-level), bokeh.models.tools (top-level)
missing module named bokeh.models.annotations.Span - imported by bokeh.models.annotations (top-level), bokeh.models.tools (top-level)
missing module named bokeh.models.annotations.PolyAnnotation - imported by bokeh.models.annotations (top-level), bokeh.models.tools (top-level)
missing module named bokeh.models.annotations.BoxAnnotation - imported by bokeh.models.annotations (top-level), bokeh.models.tools (top-level)
missing module named polars - imported by narwhals.dependencies (conditional), narwhals._utils (conditional), narwhals.series (conditional), narwhals._arrow.dataframe (delayed, conditional), narwhals.schema (delayed, conditional), narwhals._pandas_like.series (delayed, conditional), narwhals._pandas_like.dataframe (delayed, conditional), narwhals._polars.dataframe (top-level), narwhals._polars.namespace (top-level), narwhals._polars.expr (top-level), narwhals._polars.utils (top-level), narwhals._polars.series (top-level), narwhals.stable.v1.dependencies (conditional), narwhals._dask.dataframe (delayed, conditional), narwhals._arrow.series (delayed, conditional), narwhals.dataframe (conditional), narwhals._compliant.series (conditional), narwhals._compliant.dataframe (conditional), narwhals._namespace (conditional), narwhals._spark_like.dataframe (delayed, conditional)
missing module named 'sqlframe.base' - imported by narwhals._spark_like.utils (delayed, conditional), narwhals._spark_like.expr_dt (conditional), narwhals._spark_like.expr_str (conditional), narwhals._spark_like.expr_struct (conditional), narwhals._spark_like.expr (delayed, conditional), narwhals._spark_like.selectors (conditional), narwhals._spark_like.namespace (delayed, conditional), narwhals._spark_like.dataframe (delayed, conditional), narwhals._spark_like.group_by (conditional), narwhals.dependencies (delayed, conditional)
missing module named 'ibis.selectors' - imported by narwhals._ibis.dataframe (delayed)
missing module named 'ibis.common' - imported by narwhals._ibis.utils (conditional)
missing module named 'ibis.expr' - imported by narwhals._ibis.dataframe (top-level), narwhals._ibis.utils (top-level), narwhals._ibis.expr_dt (conditional), narwhals._ibis.expr_str (top-level), narwhals._ibis.expr_struct (conditional), narwhals._ibis.expr (conditional), narwhals._ibis.namespace (top-level), narwhals._ibis.selectors (conditional), narwhals._ibis.group_by (conditional)
missing module named ibis - imported by narwhals.dependencies (conditional), narwhals.stable.v1.dependencies (conditional), narwhals._polars.dataframe (delayed, conditional), narwhals._ibis.dataframe (top-level), narwhals._ibis.utils (top-level), narwhals._ibis.expr (top-level), narwhals._ibis.expr_str (top-level), narwhals._ibis.namespace (top-level), narwhals._pandas_like.dataframe (delayed, conditional), narwhals._arrow.dataframe (delayed, conditional)
missing module named dask_expr - imported by narwhals._dask.utils (conditional, optional), narwhals._dask.group_by (conditional, optional)
missing module named 'pyarrow._stubs_typing' - imported by narwhals._arrow.typing (conditional)
missing module named 'pyarrow.__lib_pxi' - imported by narwhals._arrow.typing (conditional)
missing module named 'dask.dataframe' - imported by narwhals.dependencies (conditional), narwhals.stable.v1.dependencies (conditional), narwhals._polars.dataframe (delayed, conditional), narwhals._dask.dataframe (top-level), narwhals._dask.utils (conditional, optional), narwhals._dask.expr_dt (conditional), narwhals._dask.expr_str (top-level), narwhals._dask.expr (conditional), narwhals._dask.namespace (top-level), narwhals._dask.selectors (conditional), narwhals._dask.group_by (top-level), narwhals._pandas_like.dataframe (delayed, conditional), narwhals._arrow.dataframe (delayed, conditional)
missing module named 'duckdb.typing' - imported by narwhals._duckdb.utils (conditional), narwhals._duckdb.expr (top-level), narwhals._duckdb.namespace (top-level), narwhals._duckdb.dataframe (conditional)
missing module named 'polars.lazyframe' - imported by narwhals._polars.group_by (conditional)
missing module named 'polars.dataframe' - imported by narwhals._polars.group_by (conditional)
missing module named 'modin.pandas' - imported by narwhals._pandas_like.dataframe (delayed, conditional), narwhals.stable.v1.dependencies (conditional)
missing module named 'pyspark.sql' - imported by narwhals.dependencies (delayed, conditional, optional), narwhals._namespace (conditional), narwhals._spark_like.utils (delayed, conditional)
missing module named duckdb - imported by narwhals.dependencies (conditional), narwhals._polars.dataframe (delayed, conditional), narwhals._duckdb.dataframe (top-level), narwhals._duckdb.utils (top-level), narwhals._duckdb.expr (top-level), narwhals._duckdb.expr_dt (conditional), narwhals._duckdb.expr_str (conditional), narwhals._duckdb.namespace (top-level), narwhals._duckdb.selectors (conditional), narwhals._duckdb.typing (conditional), narwhals._duckdb.group_by (conditional), narwhals._duckdb.series (conditional), narwhals._pandas_like.dataframe (delayed, conditional), narwhals._arrow.dataframe (delayed, conditional), narwhals._namespace (conditional)
missing module named cudf - imported by narwhals.dependencies (conditional), narwhals.stable.v1.dependencies (conditional)
missing module named pyspark - imported by narwhals.dependencies (conditional), narwhals._utils (delayed, conditional)
missing module named modin - imported by narwhals.dependencies (conditional)
missing module named sqlframe - imported by narwhals._utils (delayed, conditional)
missing module named bokeh.models.renderers.TileRenderer - imported by bokeh.models.renderers (top-level), bokeh.models.plots (top-level)
missing module named bokeh.models.renderers.Renderer - imported by bokeh.models.renderers (top-level), bokeh.models.plots (top-level)
missing module named bokeh.models.renderers.GlyphRenderer - imported by bokeh.models.renderers (top-level), bokeh.plotting.contour (top-level), bokeh.models.plots (top-level), bokeh.models.tools (top-level), bokeh.models.annotations.legends (top-level), bokeh.plotting.glyph_api (conditional)
missing module named bokeh.models.annotations.ColorBar - imported by bokeh.models.annotations (conditional), bokeh.models.renderers.glyph_renderer (delayed, conditional)
missing module named bokeh.models.annotations.ContourColorBar - imported by bokeh.models.annotations (conditional), bokeh.models.renderers.contour_renderer (delayed, conditional)
missing module named bokeh.models.Plot - imported by bokeh.models (conditional), bokeh.plotting._legends (conditional), bokeh.events (delayed), bokeh.plotting._figure (top-level), bokeh.plotting._tools (top-level), bokeh.layouts (top-level)
missing module named bokeh._specs - imported by bokeh.plotting._legends (conditional)
missing module named bokeh.models.GlyphRenderer - imported by bokeh.models (top-level), bokeh.plotting._renderer (top-level), bokeh.plotting._legends (conditional), bokeh.plotting._graph (top-level)
missing module named bokeh.models.ColumnarDataSource - imported by bokeh.models (top-level), bokeh.plotting._renderer (top-level), bokeh.plotting._graph (top-level)
missing module named bokeh.models.renderers.GuideRenderer - imported by bokeh.models.renderers (top-level), bokeh.models.axes (top-level), bokeh.models.grids (top-level)
missing module named bokeh.models.renderers.ContourRenderer - imported by bokeh.models.renderers (top-level), bokeh.plotting.contour (top-level)
missing module named bokeh.models.annotations.Title - imported by bokeh.models.annotations (top-level), bokeh.models.plots (top-level)
missing module named bokeh.models.annotations.Legend - imported by bokeh.models.annotations (top-level), bokeh.models.plots (top-level), bokeh.events (conditional)
missing module named bokeh.models.annotations.Annotation - imported by bokeh.models.annotations (top-level), bokeh.models.plots (top-level)
missing module named bokeh.models.annotations.LegendItem - imported by bokeh.models.annotations (conditional), bokeh.events (conditional)
missing module named 'ipykernel.comm' - imported by bokeh.io.notebook (delayed)
missing module named ipykernel - imported by bokeh.io.notebook (conditional)
missing module named 'selenium.webdriver' - imported by bokeh.io.webdriver (delayed, conditional), bokeh.io.export (delayed)
missing module named 'selenium.common' - imported by bokeh.io.export (delayed)
missing module named selenium - imported by bokeh.io.export (conditional), bokeh.io.webdriver (delayed)
missing module named bokeh.models.Axis - imported by bokeh.models (delayed), bokeh.events (delayed), bokeh.plotting._plot (top-level)
missing module named bokeh.models.LegendItem - imported by bokeh.models (top-level), bokeh.plotting._legends (top-level), services.matplotlib_chart_service (top-level)
missing module named bokeh.models.Legend - imported by bokeh.models (top-level), bokeh.plotting._legends (top-level), services.matplotlib_chart_service (top-level)
missing module named bokeh.models.ColumnDataSource - imported by bokeh.models (top-level), bokeh.plotting._renderer (top-level), bokeh.plotting._figure (top-level), bokeh.plotting._graph (top-level), bokeh.plotting (top-level), services.matplotlib_chart_service (top-level)
missing module named networkx - imported by bokeh.plotting.graph (conditional)
missing module named models.Incidencia - imported by models (delayed, optional), blueprints.dashboard.services (delayed, optional)
missing module named 'services.calendario_laboral_service' - imported by services.absence_service (optional)
missing module named 'backports.zoneinfo' - imported by alembic.script.base (conditional, optional)
missing module named backports - imported by alembic.script.base (conditional, optional)
