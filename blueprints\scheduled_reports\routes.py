# -*- coding: utf-8 -*-
from flask import render_template, request, redirect, url_for, flash, jsonify
from . import scheduled_reports_bp
from services.scheduled_report_service import ScheduledReportService
from services.report_service import ReportService
import logging

# Inicializar servicios
scheduled_service = ScheduledReportService()
report_service = ReportService()

@scheduled_reports_bp.route('/')
def index():
    """Página principal de informes programados"""
    try:
        scheduled_reports = scheduled_service.get_all_scheduled_reports()
        stats = scheduled_service.get_scheduled_report_stats()
        
        return render_template('scheduled_reports/index.html',
                             scheduled_reports=scheduled_reports,
                             stats=stats)
    except Exception as e:
        logging.error(f"Error al cargar informes programados: {str(e)}")
        flash('Error al cargar los informes programados', 'error')
        return redirect(url_for('dashboard.index'))

@scheduled_reports_bp.route('/crear', methods=['GET', 'POST'])
def create():
    """Crear un nuevo informe programado"""
    if request.method == 'POST':
        try:
            name = request.form.get('name')
            report_type = request.form.get('report_type')
            format = request.form.get('format', 'pdf')
            frequency = request.form.get('frequency')
            day_of_week = request.form.get('day_of_week', type=int)
            day_of_month = request.form.get('day_of_month', type=int)
            hour = request.form.get('hour', type=int, default=9)
            minute = request.form.get('minute', type=int, default=0)
            recipients = request.form.get('recipients')
            is_active = request.form.get('is_active') == 'on'
            
            # Validar campos requeridos
            if not name or not report_type or not frequency:
                flash('Los campos nombre, tipo de informe y frecuencia son obligatorios', 'error')
                return redirect(url_for('scheduled_reports.create'))
            
            # Crear informe programado
            scheduled_report = scheduled_service.create_scheduled_report(
                name=name,
                report_type=report_type,
                format=format,
                frequency=frequency,
                day_of_week=day_of_week if frequency == 'weekly' else None,
                day_of_month=day_of_month if frequency == 'monthly' else None,
                hour=hour,
                minute=minute,
                recipients=recipients,
                is_active=is_active
            )
            
            flash(f'Informe programado "{scheduled_report.name}" creado correctamente', 'success')
            return redirect(url_for('scheduled_reports.index'))
            
        except Exception as e:
            logging.error(f"Error al crear informe programado: {str(e)}")
            flash('Error al crear el informe programado', 'error')
            return redirect(url_for('scheduled_reports.create'))
    
    # GET request - mostrar formulario
    report_types = report_service.get_report_types()
    return render_template('scheduled_reports/create.html', report_types=report_types)

@scheduled_reports_bp.route('/<int:report_id>/editar', methods=['GET', 'POST'])
def edit(report_id):
    """Editar un informe programado"""
    scheduled_report = scheduled_service.get_scheduled_report_by_id(report_id)
    if not scheduled_report:
        flash('Informe programado no encontrado', 'error')
        return redirect(url_for('scheduled_reports.index'))
    
    if request.method == 'POST':
        try:
            name = request.form.get('name')
            report_type = request.form.get('report_type')
            format = request.form.get('format', 'pdf')
            frequency = request.form.get('frequency')
            day_of_week = request.form.get('day_of_week', type=int)
            day_of_month = request.form.get('day_of_month', type=int)
            hour = request.form.get('hour', type=int, default=9)
            minute = request.form.get('minute', type=int, default=0)
            recipients = request.form.get('recipients')
            is_active = request.form.get('is_active') == 'on'
            
            # Validar campos requeridos
            if not name or not report_type or not frequency:
                flash('Los campos nombre, tipo de informe y frecuencia son obligatorios', 'error')
                return render_template('scheduled_reports/edit.html', 
                                     scheduled_report=scheduled_report,
                                     report_types=report_service.get_report_types())
            
            # Actualizar informe programado
            updated_report = scheduled_service.update_scheduled_report(
                report_id=report_id,
                name=name,
                report_type=report_type,
                format=format,
                frequency=frequency,
                day_of_week=day_of_week if frequency == 'weekly' else None,
                day_of_month=day_of_month if frequency == 'monthly' else None,
                hour=hour,
                minute=minute,
                recipients=recipients,
                is_active=is_active
            )
            
            flash(f'Informe programado "{updated_report.name}" actualizado correctamente', 'success')
            return redirect(url_for('scheduled_reports.index'))
            
        except Exception as e:
            logging.error(f"Error al actualizar informe programado {report_id}: {str(e)}")
            flash('Error al actualizar el informe programado', 'error')
    
    # GET request - mostrar formulario
    report_types = report_service.get_report_types()
    return render_template('scheduled_reports/edit.html', 
                         scheduled_report=scheduled_report,
                         report_types=report_types)

@scheduled_reports_bp.route('/<int:report_id>/eliminar', methods=['POST'])
def delete(report_id):
    """Eliminar un informe programado"""
    try:
        if scheduled_service.delete_scheduled_report(report_id):
            flash('Informe programado eliminado correctamente', 'success')
        else:
            flash('Informe programado no encontrado', 'error')
    except Exception as e:
        logging.error(f"Error al eliminar informe programado {report_id}: {str(e)}")
        flash('Error al eliminar el informe programado', 'error')
    
    return redirect(url_for('scheduled_reports.index'))

@scheduled_reports_bp.route('/<int:report_id>/toggle', methods=['POST'])
def toggle(report_id):
    """Activar/desactivar un informe programado"""
    try:
        report = scheduled_service.toggle_scheduled_report(report_id)
        if report:
            status = "activado" if report.is_active else "desactivado"
            flash(f'Informe programado {status} correctamente', 'success')
        else:
            flash('Informe programado no encontrado', 'error')
    except Exception as e:
        logging.error(f"Error al cambiar estado de informe programado {report_id}: {str(e)}")
        flash('Error al cambiar el estado del informe programado', 'error')
    
    return redirect(url_for('scheduled_reports.index'))

@scheduled_reports_bp.route('/ejecutar/<int:report_id>')
def run_now(report_id):
    """Ejecutar un informe programado inmediatamente"""
    try:
        report = scheduled_service.get_scheduled_report_by_id(report_id)
        if not report:
            flash('Informe programado no encontrado', 'error')
            return redirect(url_for('scheduled_reports.index'))
        
        # Aquí se implementaría la lógica para generar el informe inmediatamente
        # Por ahora, solo mostramos un mensaje
        flash(f'Informe "{report.name}" programado para ejecución inmediata', 'info')
        
        # Marcar como ejecutado
        scheduled_service.mark_as_run(report_id)
        
    except Exception as e:
        logging.error(f"Error al ejecutar informe programado {report_id}: {str(e)}")
        flash('Error al ejecutar el informe programado', 'error')
    
    return redirect(url_for('scheduled_reports.index'))