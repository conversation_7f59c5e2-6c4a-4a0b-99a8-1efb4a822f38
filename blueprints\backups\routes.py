# -*- coding: utf-8 -*-
from flask import render_template, redirect, url_for, flash, request, send_file, jsonify, current_app
from . import backups_bp
import logging
import os
import zipfile
from datetime import datetime
from flask_login import login_required

# No inicializamos el servicio aquí, lo obtendremos de current_app.backup_service

@backups_bp.route('/')
@login_required
def index():
    """
    Página principal de gestión de backups
    """
    try:
        backups = current_app.backup_service.get_all_backups()

        # Adaptar los datos a lo que espera la plantilla (nombre, fecha, tamano, tipo, databases)
        backups_view = []
        for b in backups:
            try:
                # Obtener fecha como datetime
                created_at = None
                if b.get('created_at'):
                    try:
                        created_at = datetime.fromisoformat(b['created_at'])
                    except Exception:
                        created_at = None
                if not created_at and b.get('path') and os.path.exists(b['path']):
                    created_at = datetime.fromtimestamp(os.path.getmtime(b['path']))

                # Mapear tipo a valores esperados por la plantilla
                raw_type = b.get('type') or b.get('tipo')
                if raw_type == 'zip' or raw_type is None:
                    tipo = 'completo'
                elif raw_type == 'legacy_sql' or raw_type == 'legacy':
                    tipo = 'legacy'
                else:
                    # Para single_db u otros, mostrar como completo para mantener compatibilidad visual
                    tipo = 'completo'

                backups_view.append({
                    'nombre': b.get('filename') or b.get('nombre') or '',
                    'fecha': created_at or datetime.now(),
                    'tamano': round(b.get('size', b.get('tamano', 0)) or 0, 2),
                    'tipo': tipo,
                    'databases': b.get('databases', [])
                })
            except Exception as e:
                current_app.logger.error(f"Error al procesar backup para vista: {str(e)}")

        # Ordenar por fecha (más reciente primero)
        backups_view.sort(key=lambda x: x['fecha'], reverse=True)

        return render_template('backups/index.html', backups=backups_view)
    except Exception as e:
        current_app.logger.error(f"Error al listar backups: {str(e)}")
        flash("Error al cargar la lista de copias de seguridad", "error")
        return redirect(url_for('main.index'))

@backups_bp.route('/crear', methods=['GET', 'POST'])
@login_required
def create():
    """
    Crear una nueva copia de seguridad
    """
    if request.method == 'POST':
        try:
            # Obtener parámetros del formulario
            descripcion = request.form.get('descripcion', '').strip()
            etiquetas = [tag.strip() for tag in request.form.get('etiquetas', '').split(',') if tag.strip()]
            
            # Crear copia de seguridad (la implementación actual no acepta descripción/etiquetas)
            result = current_app.backup_service.create_backup()

            if result['success']:
                flash("Copia de seguridad creada correctamente", "success")
                
                # Registrar en el log de actividad
                from models import HistorialCambios
                from flask_login import current_user
                
                with current_app.app_context():
                    historial = HistorialCambios(
                        tipo_cambio='CREAR',
                        entidad='Backup',
                        entidad_id=0,
                        descripcion=f"Usuario {current_user.id} - Creada copia de seguridad: {os.path.basename(result.get('path', ''))}"
                    )
                    from app import db
                    db.session.add(historial)
                    db.session.commit()
                
                if result.get('old_backups_removed'):
                    flash(f"Se eliminaron {result['old_backups_removed']} copias de seguridad antiguas para mantener el límite", "info")
            else:
                flash(f"Error al crear la copia de seguridad: {result.get('message', 'Error desconocido')}", "error")

        except Exception as e:
            current_app.logger.error(f"Error en crear_backup: {str(e)}", exc_info=True)
            flash(f"Error al crear la copia de seguridad: {str(e)}", "error")
    
    return redirect(url_for('backups.index'))

@backups_bp.route('/restaurar/<path:filename>', methods=['POST'])
@login_required
def restore(filename):
    """
    Restaurar una copia de seguridad completa

    Args:
        filename (str): Nombre del archivo de backup a restaurar
    """
    try:
        # Validar que el archivo sea seguro
        if not filename.endswith('.zip') or '..' in filename or filename.startswith('/') or '\\' in filename or '//' in filename:
            flash("Nombre de archivo no válido", "error")
            return redirect(url_for('backups.index'))
        
        # Verificar que el archivo existe
        backups_dir = current_app.backup_service._get_backup_folder()
        backup_path = os.path.join(backups_dir, filename)
        if not os.path.exists(backup_path):
            flash("El archivo de copia de seguridad no existe", "error")
            return redirect(url_for('backups.index'))
        
        # Validar contenidos del ZIP antes de restaurar
        try:
            with zipfile.ZipFile(backup_path, 'r') as zf:
                names = zf.namelist()
                if 'backup_metadata.json' not in names:
                    flash("El archivo de copia no contiene metadatos (backup_metadata.json)", "error")
                    return redirect(url_for('backups.index'))
                db_members = [n for n in names if n.lower().endswith('.db')]
                if len(db_members) == 0:
                    flash("La copia de seguridad no contiene bases de datos", "error")
                    return redirect(url_for('backups.index'))
        except zipfile.BadZipFile:
            flash("El archivo de copia de seguridad está dañado o no es un ZIP válido", "error")
            return redirect(url_for('backups.index'))

        # Realizar la restauración
        result = current_app.backup_service.restore_backup(filename)

        if result['success']:
            flash("Base de datos restaurada correctamente", "success")
            
            # Registrar en el log de actividad
            from models import HistorialCambios
            from flask_login import current_user
            
            with current_app.app_context():
                historial = HistorialCambios(
                    tipo_cambio='RESTAURAR',
                    entidad='Backup',
                    entidad_id=0,
                    descripcion=f"Usuario {current_user.id} - Base de datos restaurada desde: {filename}"
                )
                from app import db
                db.session.add(historial)
                db.session.commit()
        else:
            flash(f"Error al restaurar: {result.get('message', 'Error desconocido')}", "error")

    except Exception as e:
        current_app.logger.error(f"Error en restore_backup: {str(e)}", exc_info=True)
        flash(f"Error al restaurar la copia de seguridad: {str(e)}", "error")

    return redirect(url_for('backups.index'))

@backups_bp.route('/restaurar/<path:filename>/<database>', methods=['POST'])
@login_required
def restore_specific_db(filename, database):
    """
    Restaurar una base de datos específica desde una copia de seguridad

    Args:
        filename (str): Nombre del archivo de backup a restaurar
        database (str): Nombre de la base de datos a restaurar
    """
    try:
        # Validar que el archivo sea seguro
        if not filename.endswith('.zip') or '..' in filename or filename.startswith('/') or '\\' in filename or '//' in filename:
            flash("Nombre de archivo no válido", "error")
            return redirect(url_for('backups.index'))
        
        # Verificar que el archivo existe
        backups_dir = current_app.backup_service._get_backup_folder()
        backup_path = os.path.join(backups_dir, filename)
        if not os.path.exists(backup_path):
            flash("El archivo de copia de seguridad no existe", "error")
            return redirect(url_for('backups.index'))
        
        # Validar que el ZIP contenga la base solicitada
        try:
            with zipfile.ZipFile(backup_path, 'r') as zf:
                names = zf.namelist()
                if 'backup_metadata.json' not in names:
                    flash("El archivo de copia no contiene metadatos (backup_metadata.json)", "error")
                    return redirect(url_for('backups.index'))
                candidate = database
                if candidate not in names:
                    # admitir rutas relativas dentro del zip
                    found = [n for n in names if n.endswith('/' + candidate) or n.endswith('\\' + candidate) or n == candidate]
                    if not found:
                        flash(f"La base de datos '{database}' no se encuentra en la copia de seguridad", "error")
                        return redirect(url_for('backups.index'))
        except zipfile.BadZipFile:
            flash("El archivo de copia de seguridad está dañado o no es un ZIP válido", "error")
            return redirect(url_for('backups.index'))
        
        # Realizar la restauración
        result = current_app.backup_service.restore_specific_db(filename, database)

        if result['success']:
            flash(f"Base de datos '{database}' restaurada correctamente", "success")
            
            # Registrar en el log de actividad
            from models import HistorialCambios
            from flask_login import current_user
            
            with current_app.app_context():
                historial = HistorialCambios(
                    tipo_cambio='RESTAURAR',
                    entidad='Backup',
                    entidad_id=0,
                    descripcion=f"Usuario {current_user.id} - Restaurada base de datos '{database}' desde: {filename}"
                )
                from app import db
                db.session.add(historial)
                db.session.commit()
        else:
            flash(f"Error al restaurar {database}: {result.get('message', 'Error desconocido')}", "error")

    except Exception as e:
        current_app.logger.error(f"Error en restore_specific_db: {str(e)}", exc_info=True)
        flash(f"Error al restaurar la base de datos {database}: {str(e)}", "error")

    return redirect(url_for('backups.index'))

@backups_bp.route('/eliminar/<filename>')
def delete(filename):
    """
    Eliminar una copia de seguridad

    Args:
        filename (str): Nombre del archivo de backup a eliminar
    """
    try:
        from flask import current_app
        if not filename.endswith('.zip') or '..' in filename or filename.startswith('/') or '\\' in filename or '//' in filename:
            flash("Nombre de archivo no válido", "error")
            return redirect(url_for('backups.index'))
        backups_dir = current_app.backup_service._get_backup_folder()
        backup_path = os.path.join(backups_dir, filename)
        if not os.path.exists(backup_path):
            flash("El archivo de copia de seguridad no existe", "error")
            return redirect(url_for('backups.index'))
        os.remove(backup_path)
        logging.info(f"Copia de seguridad {filename} eliminada correctamente")
        flash("Copia de seguridad eliminada correctamente", "success")
    except Exception as e:
        logging.error(f"Error al eliminar la copia de seguridad: {str(e)}")
        flash(f"Error al eliminar la copia de seguridad: {str(e)}", "error")
    return redirect(url_for('backups.index'))

@backups_bp.route('/descargar/<path:filename>')
@login_required
def download(filename):
    """
    Descargar una copia de seguridad

    Args:
        filename (str): Nombre del archivo de backup a descargar
    """
    try:
        # Validar que el archivo sea seguro
        if not filename.endswith('.zip') or '..' in filename or filename.startswith('/') or '\\' in filename or '//' in filename:
            flash("Nombre de archivo no válido", "error")
            return redirect(url_for('backups.index'))
        
        # Verificar que el archivo existe
        backup_path = os.path.join(current_app.backup_service.backup_dir, filename)
        if not os.path.exists(backup_path):
            flash("El archivo de copia de seguridad no existe", "error")
            return redirect(url_for('backups.index'))
        
        # Registrar la descarga en el log de actividad
        from models import HistorialCambios
        from flask_login import current_user
        
        with current_app.app_context():
            historial = HistorialCambios(
                tabla_afectada='backup',
                id_afectado=0,
                accion='descargar',
                usuario=current_user.id,
                detalles=f"Descargada copia de seguridad: {filename}"
            )
            from app import db
            db.session.add(historial)
            db.session.commit()
        
        # Devolver el archivo para descarga
        return current_app.backup_service.download_backup(filename)

    except Exception as e:
        current_app.logger.error(f"Error en download_backup: {str(e)}", exc_info=True)
        flash(f"Error al descargar la copia de seguridad: {str(e)}", "error")
        return redirect(url_for('backups.index'))

@backups_bp.route('/limpiar', methods=['POST'])
def clean_database():
    """
    Limpiar la base de datos principal (eliminar todos los registros pero mantener la estructura)
    """
    # Verificar confirmación
    confirmacion = request.form.get('confirmacion', '').lower()
    if confirmacion != 'confirmar':
        flash("Debe escribir 'confirmar' para proceder con la limpieza de la base de datos", "warning")
        return redirect(url_for('backups.index'))

    try:
        result = current_app.backup_service.clean_database()

        if result['success']:
            flash(result['message'], "success")
        else:
            flash(result['message'], "error")

    except Exception as e:
        flash(f"Error al limpiar la base de datos: {str(e)}", "error")
        logging.error(f"Error al limpiar la base de datos: {str(e)}")

    return redirect(url_for('backups.index'))

@backups_bp.route('/limpiar/<database>', methods=['POST'])
def clean_specific_database(database):
    """
    Limpiar una base de datos específica (eliminar todos los registros pero mantener la estructura)

    Args:
        database (str): Nombre de la base de datos a limpiar
    """
    # Verificar confirmación
    confirmacion = request.form.get('confirmacion', '').lower()
    if confirmacion != 'confirmar':
        flash(f"Debe escribir 'confirmar' para proceder con la limpieza de la base de datos {database}", "warning")
        return redirect(url_for('backups.database_info'))

    try:
        result = current_app.backup_service.clean_database(specific_db=database)

        if result['success']:
            flash(result['message'], "success")
        else:
            flash(result['message'], "error")

    except Exception as e:
        flash(f"Error al limpiar la base de datos {database}: {str(e)}", "error")
        logging.error(f"Error al limpiar la base de datos {database}: {str(e)}")

    return redirect(url_for('backups.database_info'))

@backups_bp.route('/verificar-compatibilidad/<filename>')
def check_compatibility(filename):
    """
    Verificar la compatibilidad de una copia de seguridad con la estructura actual

    Args:
        filename (str): Nombre del archivo de backup a verificar
    """
    try:
        result = current_app.backup_service.check_backup_compatibility(filename)

        if result['success']:
            if result['compatible']:
                flash(f"La copia de seguridad {filename} es compatible con la estructura actual", "success")
            else:
                flash(f"La copia de seguridad {filename} NO es compatible con la estructura actual", "warning")
        else:
            flash(result['message'], "error")

        return render_template('backups/compatibility_result.html', result=result, filename=filename)
    except Exception as e:
        flash(f"Error al verificar compatibilidad: {str(e)}", "error")
        logging.error(f"Error en check_compatibility: {str(e)}")
        return redirect(url_for('backups.index'))

@backups_bp.route('/verificar-todas')
def check_all_compatibility():
    """
    Verificar la compatibilidad de todas las copias de seguridad disponibles
    """
    try:
        result = current_app.backup_service.check_all_backups_compatibility()

        if result['success']:
            flash(result['message'], "info")
        else:
            flash(result['message'], "error")

        return render_template('backups/compatibility_all.html', result=result)
    except Exception as e:
        flash(f"Error al verificar compatibilidad de todas las copias: {str(e)}", "error")
        logging.error(f"Error en check_all_compatibility: {str(e)}")
        return redirect(url_for('backups.index'))

@backups_bp.route('/api/verificar-compatibilidad/<filename>')
def api_check_compatibility(filename):
    """
    API para verificar la compatibilidad de una copia de seguridad

    Args:
        filename (str): Nombre del archivo de backup a verificar
    """
    try:
        result = current_app.backup_service.check_backup_compatibility(filename)
        return jsonify(result)
    except Exception as e:
        logging.error(f"Error en api_check_compatibility: {str(e)}")
        return jsonify({
            'success': False,
            'compatible': False,
            'message': f"Error al verificar compatibilidad: {str(e)}"
        })

@backups_bp.route('/info-bd')
def database_info():
    """
    Obtiene información básica sobre las bases de datos disponibles
    """
    try:
        # Buscar todas las bases de datos
        databases = current_app.backup_service.find_databases()

        if not databases:
            flash("No se encontraron bases de datos", "warning")

        return render_template('backups/database_info.html', databases=databases)
    except Exception as e:
        flash(f"Error al obtener información de las bases de datos: {str(e)}", "error")
        logging.error(f"Error en database_info: {str(e)}")
        return redirect(url_for('backups.index'))

@backups_bp.route('/estructura-bd')
def database_structure():
    """
    Obtiene información detallada sobre la estructura de las bases de datos
    """
    try:
        # Obtener parámetros de la URL
        db_path = request.args.get('db_path', None)

        # Obtener estructura de la base de datos
        result = current_app.backup_service.get_database_structure(db_path)

        if request.args.get('format', '') == 'json':
            return jsonify(result)
        else:
            if result['success']:
                flash("Información de estructura de base de datos obtenida correctamente", "success")
            else:
                flash(result['message'], "error")

            return render_template('backups/database_structure.html', result=result)
    except Exception as e:
        flash(f"Error al obtener estructura de la base de datos: {str(e)}", "error")
        logging.error(f"Error en database_structure: {str(e)}")
        return redirect(url_for('backups.index'))

@backups_bp.route('/verificar-integridad-calendario')
def check_calendario_integrity():
    """
    Verificar la integridad específica de las tablas del calendario laboral
    """
    try:
        # Buscar la base de datos unificada
        databases = current_app.backup_service.find_databases()
        unified_db = None
        
        for db in databases:
            if db['name'] == 'unified_app.db':
                unified_db = db
                break
        
        if not unified_db:
            flash("No se encontró la base de datos unificada", "error")
            return redirect(url_for('backups.index'))
        
        # Verificar integridad del calendario laboral
        result = current_app.backup_service.check_calendario_laboral_integrity(unified_db['path'])
        
        if result['success']:
            flash("La integridad del calendario laboral es correcta", "success")
        else:
            flash(f"Problemas de integridad encontrados: {result['message']}", "warning")
        
        return render_template('backups/integrity_result.html', result=result, database=unified_db)
        
    except Exception as e:
        flash(f"Error al verificar integridad: {str(e)}", "error")
        logging.error(f"Error en check_calendario_integrity: {str(e)}")
        return redirect(url_for('backups.index'))
