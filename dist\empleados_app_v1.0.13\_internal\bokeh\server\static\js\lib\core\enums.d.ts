export declare const Align: import("./kinds").Kinds.Enum<"start" | "center" | "end">;
export type Align = typeof Align["__type__"];
export declare const HAlign: import("./kinds").Kinds.Enum<"center" | "left" | "right">;
export type HAlign = typeof HAlign["__type__"];
export declare const VAlign: import("./kinds").Kinds.Enum<"center" | "top" | "bottom">;
export type VAlign = typeof VAlign["__type__"];
export declare const Anchor: import("./kinds").Kinds.Enum<"center" | "left" | "right" | "top" | "bottom" | "top_left" | "top_center" | "top_right" | "center_left" | "center_center" | "center_right" | "bottom_left" | "bottom_center" | "bottom_right">;
export type Anchor = typeof Anchor["__type__"];
export declare const AngleUnits: import("./kinds").Kinds.Enum<"deg" | "rad" | "grad" | "turn">;
export type AngleUnits = typeof AngleUnits["__type__"];
export declare const AlternationPolicy: import("./kinds").Kinds.Enum<"every" | "none" | "even" | "odd">;
export type AlternationPolicy = typeof AlternationPolicy["__type__"];
export declare const BoxOrigin: import("./kinds").Kinds.Enum<"center" | "corner">;
export type BoxOrigin = typeof BoxOrigin["__type__"];
export declare const ButtonType: import("./kinds").Kinds.Enum<"default" | "primary" | "success" | "warning" | "danger" | "light">;
export type ButtonType = typeof ButtonType["__type__"];
export declare const CalendarPosition: import("./kinds").Kinds.Enum<"auto" | "above" | "below">;
export type CalendarPosition = typeof CalendarPosition["__type__"];
export declare const Clock: import("./kinds").Kinds.Enum<"12h" | "24h">;
export type Clock = typeof Clock["__type__"];
export declare const CoordinateUnits: import("./kinds").Kinds.Enum<"canvas" | "data" | "screen">;
export type CoordinateUnits = typeof CoordinateUnits["__type__"];
export declare const ContextWhich: import("./kinds").Kinds.Enum<"start" | "center" | "end" | "all">;
export type ContextWhich = typeof ContextWhich["__type__"];
export declare const Dimension: import("./kinds").Kinds.Enum<"width" | "height">;
export type Dimension = typeof Dimension["__type__"];
export declare const Dimensions: import("./kinds").Kinds.Enum<"width" | "height" | "both">;
export type Dimensions = typeof Dimensions["__type__"];
export declare const Direction: import("./kinds").Kinds.Enum<"clock" | "anticlock">;
export type Direction = typeof Direction["__type__"];
export declare const Distribution: import("./kinds").Kinds.Enum<"uniform" | "normal">;
export type Distribution = typeof Distribution["__type__"];
export declare const Face: import("./kinds").Kinds.Enum<"front" | "back">;
export type Face = typeof Face["__type__"];
export declare const FlowMode: import("./kinds").Kinds.Enum<"block" | "inline">;
export type FlowMode = typeof FlowMode["__type__"];
export declare const FontStyle: import("./kinds").Kinds.Enum<"bold" | "normal" | "italic" | "bold italic">;
export type FontStyle = typeof FontStyle["__type__"];
export declare const HatchPatternType: import("./kinds").Kinds.Enum<"blank" | "dot" | "ring" | "horizontal_line" | "vertical_line" | "cross" | "horizontal_dash" | "vertical_dash" | "spiral" | "right_diagonal_line" | "left_diagonal_line" | "diagonal_cross" | "right_diagonal_dash" | "left_diagonal_dash" | "horizontal_wave" | "vertical_wave" | "criss_cross" | " " | "." | "o" | "-" | "|" | "+" | "\"" | ":" | "@" | "/" | "\\" | "x" | "," | "`" | "v" | ">" | "*">;
export type HatchPatternType = typeof HatchPatternType["__type__"];
export declare const BuiltinFormatter: import("./kinds").Kinds.Enum<"raw" | "basic" | "numeral" | "printf" | "datetime">;
export type BuiltinFormatter = typeof BuiltinFormatter["__type__"];
export declare const HTTPMethod: import("./kinds").Kinds.Enum<"POST" | "GET">;
export type HTTPMethod = typeof HTTPMethod["__type__"];
export declare const HexTileOrientation: import("./kinds").Kinds.Enum<"pointytop" | "flattop">;
export type HexTileOrientation = typeof HexTileOrientation["__type__"];
export declare const HoverMode: import("./kinds").Kinds.Enum<"mouse" | "hline" | "vline">;
export type HoverMode = typeof HoverMode["__type__"];
export declare const ImageOrigin: import("./kinds").Kinds.Enum<"top_left" | "top_right" | "bottom_left" | "bottom_right">;
export type ImageOrigin = typeof ImageOrigin["__type__"];
export declare const LatLon: import("./kinds").Kinds.Enum<"lat" | "lon">;
export type LatLon = typeof LatLon["__type__"];
export declare const LegendClickPolicy: import("./kinds").Kinds.Enum<"none" | "hide" | "mute">;
export type LegendClickPolicy = typeof LegendClickPolicy["__type__"];
export declare const LegendLocation: import("./kinds").Kinds.Enum<"center" | "left" | "right" | "top" | "bottom" | "top_left" | "top_center" | "top_right" | "center_left" | "center_center" | "center_right" | "bottom_left" | "bottom_center" | "bottom_right">;
export type LegendLocation = Anchor;
export declare const LineCap: import("./kinds").Kinds.Enum<"round" | "butt" | "square">;
export type LineCap = typeof LineCap["__type__"];
export declare const LineDash: import("./kinds").Kinds.Enum<"solid" | "dashed" | "dotted" | "dotdash" | "dashdot">;
export type LineDash = typeof LineDash["__type__"];
export declare const LineJoin: import("./kinds").Kinds.Enum<"round" | "miter" | "bevel">;
export type LineJoin = typeof LineJoin["__type__"];
export declare const LinePolicy: import("./kinds").Kinds.Enum<"none" | "prev" | "next" | "nearest" | "interp">;
export type LinePolicy = typeof LinePolicy["__type__"];
export declare const Location: import("./kinds").Kinds.Enum<"left" | "right" | "above" | "below">;
export type Location = typeof Location["__type__"];
export declare const Logo: import("./kinds").Kinds.Enum<"grey" | "normal">;
export type Logo = typeof Logo["__type__"];
export declare const MapType: import("./kinds").Kinds.Enum<"satellite" | "roadmap" | "terrain" | "hybrid">;
export type MapType = typeof MapType["__type__"];
export declare const MarkerType: import("./kinds").Kinds.Enum<"dot" | "cross" | "x" | "square" | "asterisk" | "circle" | "circle_cross" | "circle_dot" | "circle_x" | "circle_y" | "dash" | "diamond" | "diamond_cross" | "diamond_dot" | "hex" | "hex_dot" | "inverted_triangle" | "plus" | "square_cross" | "square_dot" | "square_pin" | "square_x" | "star" | "star_dot" | "triangle" | "triangle_dot" | "triangle_pin" | "y">;
export type MarkerType = typeof MarkerType["__type__"];
export declare const MutedPolicy: import("./kinds").Kinds.Enum<"show" | "ignore">;
export type MutedPolicy = typeof MutedPolicy["__type__"];
export declare const Orientation: import("./kinds").Kinds.Enum<"vertical" | "horizontal">;
export type Orientation = typeof Orientation["__type__"];
export declare const OutlineShapeName: import("./kinds").Kinds.Enum<"none" | "square" | "circle" | "diamond" | "triangle" | "box" | "rectangle" | "ellipse" | "trapezoid" | "parallelogram">;
export type OutlineShapeName = typeof OutlineShapeName["__type__"];
export declare const OutputBackend: import("./kinds").Kinds.Enum<"canvas" | "svg" | "webgl">;
export type OutputBackend = typeof OutputBackend["__type__"];
export declare const PaddingUnits: import("./kinds").Kinds.Enum<"percent" | "absolute">;
export type PaddingUnits = typeof PaddingUnits["__type__"];
export declare const PanDirection: import("./kinds").Kinds.Enum<"left" | "right" | "up" | "down" | "west" | "east" | "north" | "south">;
export type PanDirection = typeof PanDirection["__type__"];
export declare const Place: import("./kinds").Kinds.Enum<"center" | "left" | "right" | "above" | "below">;
export type Place = typeof Place["__type__"];
export declare const PointPolicy: import("./kinds").Kinds.Enum<"none" | "snap_to_data" | "follow_mouse">;
export type PointPolicy = typeof PointPolicy["__type__"];
export declare const RadiusDimension: import("./kinds").Kinds.Enum<"max" | "x" | "y" | "min">;
export type RadiusDimension = typeof RadiusDimension["__type__"];
export declare const RenderLevel: import("./kinds").Kinds.Enum<"image" | "underlay" | "glyph" | "guide" | "annotation" | "overlay">;
export type RenderLevel = typeof RenderLevel["__type__"];
export declare const ResetPolicy: import("./kinds").Kinds.Enum<"standard" | "event_only">;
export type ResetPolicy = typeof ResetPolicy["__type__"];
export declare const ResolutionType: import("./kinds").Kinds.Enum<"microseconds" | "milliseconds" | "seconds" | "minsec" | "minutes" | "hourmin" | "hours" | "days" | "months" | "years">;
export type ResolutionType = typeof ResolutionType["__type__"];
export declare const RoundingFunction: import("./kinds").Kinds.Enum<"round" | "ceil" | "floor" | "nearest" | "rounddown" | "roundup">;
export type RoundingFunction = typeof RoundingFunction["__type__"];
export declare const ScrollbarPolicy: import("./kinds").Kinds.Enum<"auto" | "visible" | "hidden">;
export type ScrollbarPolicy = typeof ScrollbarPolicy["__type__"];
export declare const RegionSelectionMode: import("./kinds").Kinds.Enum<"replace" | "append" | "intersect" | "subtract" | "xor">;
export type RegionSelectionMode = typeof RegionSelectionMode["__type__"];
export declare const SelectionMode: import("./kinds").Kinds.Enum<"replace" | "append" | "intersect" | "subtract" | "xor" | "toggle">;
export type SelectionMode = typeof SelectionMode["__type__"];
export declare const Side: import("./kinds").Kinds.Enum<"left" | "right" | "above" | "below">;
export type Side = typeof Side["__type__"];
export declare const SizingMode: import("./kinds").Kinds.Enum<"fixed" | "stretch_width" | "stretch_height" | "stretch_both" | "scale_width" | "scale_height" | "scale_both" | "inherit">;
export type SizingMode = typeof SizingMode["__type__"];
export declare const Sort: import("./kinds").Kinds.Enum<"ascending" | "descending">;
export type Sort = typeof Sort["__type__"];
export declare const SpatialUnits: import("./kinds").Kinds.Enum<"data" | "screen">;
export type SpatialUnits = typeof SpatialUnits["__type__"];
export declare const StartEnd: import("./kinds").Kinds.Enum<"start" | "end">;
export type StartEnd = typeof StartEnd["__type__"];
export declare const StepMode: import("./kinds").Kinds.Enum<"center" | "after" | "before">;
export type StepMode = typeof StepMode["__type__"];
export declare const TapBehavior: import("./kinds").Kinds.Enum<"select" | "inspect">;
export type TapBehavior = typeof TapBehavior["__type__"];
export declare const TapGesture: import("./kinds").Kinds.Enum<"tap" | "doubletap">;
export type TapGesture = typeof TapGesture["__type__"];
export declare const TextAlign: import("./kinds").Kinds.Enum<"center" | "left" | "right">;
export type TextAlign = typeof TextAlign["__type__"];
export declare const TextBaseline: import("./kinds").Kinds.Enum<"top" | "bottom" | "middle" | "alphabetic" | "hanging" | "ideographic">;
export type TextBaseline = typeof TextBaseline["__type__"];
export declare const TextureRepetition: import("./kinds").Kinds.Enum<"repeat" | "repeat_x" | "repeat_y" | "no_repeat">;
export type TextureRepetition = typeof TextureRepetition["__type__"];
export declare const LabelOrientation: import("./kinds").Kinds.Enum<"normal" | "vertical" | "horizontal" | "parallel">;
export type LabelOrientation = typeof LabelOrientation["__type__"];
export declare const TooltipAttachment: import("./kinds").Kinds.Enum<"left" | "right" | "above" | "below" | "vertical" | "horizontal">;
export type TooltipAttachment = typeof TooltipAttachment["__type__"];
export declare const UpdateMode: import("./kinds").Kinds.Enum<"replace" | "append">;
export type UpdateMode = typeof UpdateMode["__type__"];
export declare const VerticalAlign: import("./kinds").Kinds.Enum<"top" | "bottom" | "middle">;
export type VerticalAlign = typeof VerticalAlign["__type__"];
export declare const WindowAxis: import("./kinds").Kinds.Enum<"none" | "x" | "y">;
export type WindowAxis = typeof WindowAxis["__type__"];
export declare const ToolIcon: import("./kinds").Kinds.Enum<"delete" | "bold" | "italic" | "square" | "append_mode" | "arrow_down_to_bar" | "arrow_up_from_bar" | "auto_box_zoom" | "box_edit" | "box_select" | "box_zoom" | "caret_down" | "caret_left" | "caret_right" | "caret_up" | "check" | "chevron_down" | "chevron_left" | "chevron_right" | "chevron_up" | "clear_selection" | "copy" | "crosshair" | "dark_theme" | "freehand_draw" | "fullscreen" | "help" | "hover" | "intersect_mode" | "invert_selection" | "lasso_select" | "light_theme" | "line_edit" | "maximize" | "minimize" | "pan" | "pin" | "point_draw" | "pointer" | "poly_draw" | "poly_edit" | "polygon_select" | "range" | "redo" | "replace_mode" | "reset" | "save" | "see_off" | "see_on" | "settings" | "square_check" | "subtract_mode" | "tap_select" | "text_align_center" | "text_align_left" | "text_align_right" | "undo" | "unknown" | "unpin" | "wheel_pan" | "wheel_zoom" | "x_box_select" | "x_box_zoom" | "x_grip" | "x_pan" | "xor_mode" | "y_box_select" | "y_box_zoom" | "y_grip" | "y_pan" | "zoom_in" | "zoom_out">;
export type ToolIcon = typeof ToolIcon["__type__"];
export declare const ToolName: import("./kinds").Kinds.Enum<"tap" | "doubletap" | "auto_box_zoom" | "box_select" | "box_zoom" | "copy" | "crosshair" | "freehand_draw" | "fullscreen" | "help" | "hover" | "lasso_select" | "pan" | "redo" | "reset" | "save" | "undo" | "wheel_zoom" | "zoom_in" | "zoom_out" | "click" | "examine" | "pan_down" | "pan_east" | "pan_left" | "pan_north" | "pan_right" | "pan_south" | "pan_up" | "pan_west" | "poly_select" | "xbox_select" | "xbox_zoom" | "xcrosshair" | "xpan" | "xwheel_pan" | "xwheel_zoom" | "xzoom_in" | "xzoom_out" | "ybox_select" | "ybox_zoom" | "ycrosshair" | "ypan" | "ywheel_pan" | "ywheel_zoom" | "yzoom_in" | "yzoom_out">;
export type ToolName = typeof ToolName["__type__"];
//# sourceMappingURL=enums.d.ts.map