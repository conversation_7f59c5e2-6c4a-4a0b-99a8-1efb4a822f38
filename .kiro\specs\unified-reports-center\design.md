# Design Document

## Overview

El Centro de Informes Unificado es un módulo independiente que consolidará todas las funcionalidades de informes existentes en la aplicación. El diseño se basa en una arquitectura modular con servicios especializados, una interfaz moderna responsive, y un sistema de navegación simplificado que reduce la complejidad actual de múltiples ubicaciones dispersas.

## Architecture

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Layer                           │
├─────────────────────────────────────────────────────────────┤
│  Dashboard    │  RRHH     │  Polivalencia │  Absentismo    │
│  Ejecutivo    │  Reports  │  & Competenc. │  & Ausencias   │
├─────────────────────────────────────────────────────────────┤
│  Calendario   │  Análisis │  Export       │  Filter        │
│  & Turnos     │  Avanzados│  System       │  System        │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   API Layer                                 │
├─────────────────────────────────────────────────────────────┤
│  unified_reports_bp (Flask Blueprint)                      │
│  ├── Dashboard Routes                                       │
│  ├── RRHH Routes                                           │
│  ├── Polivalencia Routes                                   │
│  ├── Absentismo Routes                                     │
│  ├── Calendario Routes                                     │
│  ├── Analytics Routes                                      │
│  └── Export Routes                                         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 Service Layer                               │
├─────────────────────────────────────────────────────────────┤
│  UnifiedReportsService (Main Orchestrator)                 │
│  ├── UnifiedHRService                                      │
│  ├── UnifiedPolivalenciaService                           │
│  ├── UnifiedAbsenteeismService                            │
│  ├── UnifiedCalendarService                               │
│  ├── UnifiedAnalyticsService                              │
│  ├── UnifiedExportService                                 │
│  └── UnifiedCacheService                                  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   Data Layer                                │
├─────────────────────────────────────────────────────────────┤
│  Existing Models (No changes)                              │
│  ├── Empleado, Departamento, Sector                       │
│  ├── Polivalencia, HistorialPolivalencia                  │
│  ├── Permiso, Capacitacion                                │
│  └── CalendarioLaboral, Turno                             │
└─────────────────────────────────────────────────────────────┘
```

### Component Architecture

#### 1. Frontend Components
- **Dashboard Widgets**: Componentes reutilizables para métricas
- **Filter Panel**: Sistema de filtros global persistente
- **Chart Components**: Gráficos modernos con Chart.js
- **Export Modal**: Interface unificada de exportación
- **Navigation Breadcrumbs**: Navegación contextual

#### 2. Backend Services
- **UnifiedReportsService**: Orquestador principal
- **Specialized Services**: Servicios especializados por dominio
- **Cache Layer**: Sistema de cache inteligente
- **Export Engine**: Motor de exportación multi-formato

## Components and Interfaces

### 1. UnifiedReportsService

**Responsabilidades:**
- Orquestar llamadas a servicios especializados
- Gestionar cache global
- Coordinar filtros entre secciones
- Manejar permisos y autenticación

**Interface:**
```python
class UnifiedReportsService:
    def get_dashboard_data(self, filters: dict) -> dict
    def get_section_data(self, section: str, filters: dict) -> dict
    def apply_global_filters(self, filters: dict) -> None
    def clear_cache(self, section: str = None) -> None
    def validate_permissions(self, user: User, section: str) -> bool
```

### 2. UnifiedHRService

**Responsabilidades:**
- Consolidar datos de empleados activos/inactivos
- Generar distribuciones (cargo, sexo, antigüedad)
- Calcular KPIs de RRHH
- Análisis de rotación y contratación

**Funcionalidades Consolidadas:**
- Empleados Activos (de `report_service`)
- Empleados Inactivos (de `report_service`)
- Distribuciones (de `report_service`)
- KPIs y Métricas (de `report_service`)
- Estadísticas Generales (de `statistics_service`)

### 3. UnifiedPolivalenciaService

**Responsabilidades:**
- Dashboard de polivalencia integrado
- Análisis de cobertura por sectores
- Evolución temporal de competencias
- Análisis de competencias vs antigüedad
- Gestión de contingencias
- Predicción de necesidades de formación

**Funcionalidades Consolidadas:**
- Polivalencia General (de `statistics_service`)
- Cobertura (de `coverage_dashboard_service`)
- Evolución (de `polivalencia_evolution_service`)
- Competencias-Antigüedad (de `competence_distribution_service`)
- Contingencias (de `contingency_response_service`)
- Formación (de `training_needs_prediction_service`)
- Impacto Rotación (de `rotation_impact_service`)

### 4. UnifiedAbsenteeismService

**Responsabilidades:**
- Análisis completo de absentismo
- Gestión de permisos vigentes
- Análisis de bajas médicas (incluyendo indefinidas)
- Correlación absentismo-polivalencia
- Impacto en cobertura

**Funcionalidades Consolidadas:**
- Análisis Ausentismo (de `analytics_report_service`, `report_service`, `statistics_service`)
- Permisos Vigentes (de `report_service`)
- Bajas Indefinidas (de `indefinite_leave_service`, `report_service`)
- Impacto Absentismo (de `absenteeism_impact_service`)

### 5. UnifiedCalendarService

**Responsabilidades:**
- Estadísticas de calendario laboral
- Análisis de turnos y horarios
- Informes mensuales consolidados
- Análisis de días laborables vs ausencias

**Funcionalidades Consolidadas:**
- Informes Calendario (de `calendario/routes.py`)
- Estadísticas Calendario (de `calendario/routes.py`)
- Análisis Turnos (funcionalidad nueva)

### 6. UnifiedAnalyticsService

**Responsabilidades:**
- Análisis predictivos avanzados
- Clustering y patrones
- Correlaciones complejas
- Tendencias y forecasting

**Funcionalidades Consolidadas:**
- Performance Trends (de `analytics_service`)
- Competency Clusters (de `analytics_service`)
- Polivalencia Patterns (de `analytics_service`)
- Absenteeism Correlation (de `analytics_service`)

### 7. UnifiedExportService

**Responsabilidades:**
- Exportación individual de informes
- Exportación por secciones
- Exportación personalizada
- Generación de reportes ejecutivos

**Formatos Soportados:**
- **PDF**: Reportes visuales con gráficos
- **Excel**: Múltiples hojas por sección
- **CSV**: Datos tabulares para análisis

## Data Models

### Existing Models (No Changes)
El diseño reutiliza todos los modelos existentes sin modificaciones:

- **Empleado**: Datos básicos de empleados
- **Departamento**: Estructura organizacional
- **Sector**: Sectores de polivalencia
- **Polivalencia**: Competencias por empleado
- **HistorialPolivalencia**: Evolución de competencias
- **Permiso**: Ausencias y permisos
- **Capacitacion**: Datos de formación
- **CalendarioLaboral**: Calendario de trabajo
- **Turno**: Turnos de trabajo

### New Configuration Models

```python
class UnifiedReportConfig(db.Model):
    """Configuración personalizada de dashboards"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('usuario.id'))
    section = db.Column(db.String(50))
    widget_config = db.Column(db.JSON)
    filters_config = db.Column(db.JSON)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow)

class UnifiedReportCache(db.Model):
    """Cache inteligente para informes"""
    id = db.Column(db.Integer, primary_key=True)
    cache_key = db.Column(db.String(255), unique=True)
    section = db.Column(db.String(50))
    data = db.Column(db.JSON)
    expires_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
```

## User Interface Design

### 1. Navigation Structure

```
Centro de Informes Unificado
├── 📊 Dashboard Ejecutivo
│   ├── Widgets Configurables
│   ├── KPIs Principales
│   └── Resumen Ejecutivo
├── 👥 Recursos Humanos
│   ├── Empleados (Activos/Inactivos)
│   ├── Distribuciones
│   └── KPIs RRHH
├── 🎯 Polivalencia y Competencias
│   ├── Dashboard Polivalencia
│   ├── Cobertura por Sectores
│   ├── Evolución Temporal
│   └── Competencias vs Antigüedad
├── 🏥 Absentismo y Ausencias
│   ├── Análisis Completo
│   ├── Permisos Vigentes
│   └── Bajas Médicas
├── 📅 Calendario y Turnos
│   ├── Estadísticas Mensuales
│   └── Análisis de Turnos
└── 🔬 Análisis Avanzados
    ├── Tendencias y Predicciones
    ├── Clusters y Patrones
    └── Correlaciones
```

### 2. Dashboard Layout

```html
┌─────────────────────────────────────────────────────────────┐
│  Centro de Informes Unificado    [🔍] [⚙️] [📤]           │
├─────────────────────────────────────────────────────────────┤
│  📊 Dashboard │ 👥 RRHH │ 🎯 Polivalencia │ 🏥 Absentismo │
├─────────────────────────────────────────────────────────────┤
│  Filtros: [Departamento ▼] [Fecha ▼] [Empleado ▼] [Limpiar] │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   Widget 1  │ │   Widget 2  │ │   Widget 3  │           │
│  │   📈 KPI    │ │   📊 Chart  │ │   📋 Table  │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   Widget 4  │ │   Widget 5  │ │   Widget 6  │           │
│  │   🎯 Metric │ │   📉 Trend  │ │   🔍 Detail │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 3. Modern UI Components

**Design System:**
- **Framework**: Bootstrap 5 con customizaciones
- **Charts**: Chart.js 4.x con animaciones
- **Icons**: Font Awesome 6
- **Colors**: Paleta moderna con modo oscuro opcional
- **Typography**: Inter font para mejor legibilidad

**Key Features:**
- **Responsive Grid**: CSS Grid + Flexbox
- **Dark Mode**: Toggle automático/manual
- **Animations**: Micro-interactions suaves
- **Loading States**: Skeletons y spinners
- **Error States**: Mensajes informativos

## Error Handling

### 1. Service Layer Error Handling

```python
class UnifiedReportsException(Exception):
    """Base exception for unified reports"""
    pass

class DataNotFoundError(UnifiedReportsException):
    """When requested data is not available"""
    pass

class PermissionDeniedError(UnifiedReportsException):
    """When user lacks permissions"""
    pass

class CacheError(UnifiedReportsException):
    """Cache-related errors"""
    pass
```

### 2. Frontend Error Handling

- **Network Errors**: Retry automático con backoff
- **Data Errors**: Mensajes contextuales al usuario
- **Permission Errors**: Redirección a página de acceso
- **Validation Errors**: Feedback inmediato en formularios

### 3. Graceful Degradation

- **Partial Data**: Mostrar datos disponibles con advertencias
- **Service Unavailable**: Fallback a datos cached
- **Export Errors**: Opciones alternativas de formato

## Testing Strategy

### 1. Unit Tests

**Services Testing:**
```python
class TestUnifiedHRService:
    def test_get_active_employees_data()
    def test_get_distribution_by_role()
    def test_calculate_hr_kpis()
    def test_apply_department_filter()
    def test_handle_empty_data()

class TestUnifiedPolivalenciaService:
    def test_get_polivalencia_dashboard()
    def test_get_coverage_analysis()
    def test_get_competence_evolution()
    def test_calculate_coverage_gaps()
```

**Routes Testing:**
```python
class TestUnifiedReportsRoutes:
    def test_dashboard_access_with_permissions()
    def test_dashboard_access_without_permissions()
    def test_section_data_with_filters()
    def test_export_functionality()
    def test_cache_behavior()
```

### 2. Integration Tests

- **End-to-End Workflows**: Navegación completa usuario
- **Data Consistency**: Verificar datos entre secciones
- **Filter Persistence**: Mantener filtros entre navegación
- **Export Integration**: Generar y validar archivos

### 3. Performance Tests

- **Load Testing**: Múltiples usuarios concurrentes
- **Data Volume**: Grandes volúmenes de datos
- **Cache Efficiency**: Tiempos de respuesta con/sin cache
- **Memory Usage**: Consumo de memoria en operaciones pesadas

### 4. UI/UX Tests

- **Responsive Testing**: Diferentes tamaños de pantalla
- **Accessibility**: WCAG 2.1 compliance
- **Browser Compatibility**: Chrome, Firefox, Safari, Edge
- **User Journey**: Flujos típicos de usuario

## Performance Considerations

### 1. Caching Strategy

**Multi-Level Cache:**
```python
# Level 1: In-Memory Cache (Redis)
cache.set(f"unified_reports:{section}:{filters_hash}", data, timeout=300)

# Level 2: Database Cache
UnifiedReportCache.create(cache_key, section, data, expires_at)

# Level 3: Browser Cache
@cache_control(max_age=60, public=True)
```

**Cache Invalidation:**
- **Time-based**: TTL por tipo de dato
- **Event-based**: Invalidar cuando cambian datos fuente
- **Manual**: Botón de refresh para usuarios

### 2. Database Optimization

**Query Optimization:**
- **Eager Loading**: Cargar relaciones necesarias
- **Indexing**: Índices en campos de filtro frecuentes
- **Pagination**: Limitar resultados grandes
- **Aggregation**: Usar queries agregadas en BD

**Connection Management:**
- **Connection Pooling**: Reutilizar conexiones
- **Read Replicas**: Separar lecturas de escrituras
- **Query Monitoring**: Identificar queries lentas

### 3. Frontend Performance

**Code Splitting:**
```javascript
// Lazy loading por sección
const HRSection = lazy(() => import('./sections/HRSection'));
const PolivalenciaSection = lazy(() => import('./sections/PolivalenciaSection'));
```

**Data Loading:**
- **Progressive Loading**: Cargar datos críticos primero
- **Infinite Scroll**: Para listas grandes
- **Debounced Filters**: Evitar requests excesivos
- **Prefetching**: Precargar secciones probables

## Security Considerations

### 1. Authentication & Authorization

**Permission Inheritance:**
```python
# Reutilizar sistema existente
@login_required
@permission_required('view_reports')
def unified_dashboard():
    # Validar permisos específicos por sección
    if not current_user.can_view_hr_data():
        # Ocultar sección RRHH
```

### 2. Data Protection

**Sensitive Data Handling:**
- **Field-Level Security**: Ocultar campos según permisos
- **Data Masking**: Enmascarar datos sensibles en exports
- **Audit Trail**: Registrar accesos a datos sensibles

### 3. Export Security

**File Security:**
- **Temporary Files**: Limpiar archivos temporales
- **Access Control**: URLs firmadas para descargas
- **Content Validation**: Validar contenido antes de export

## Deployment Considerations

### 1. Rollout Strategy

**Phase 1: Soft Launch**
- Activar solo para usuarios admin
- Recopilar feedback inicial
- Ajustar performance

**Phase 2: Beta Testing**
- Abrir a usuarios seleccionados
- Monitorear uso y errores
- Refinar funcionalidades

**Phase 3: General Availability**
- Activar para todos los usuarios
- Mantener informes legacy como fallback
- Planificar migración gradual

### 2. Monitoring

**Application Monitoring:**
- **Response Times**: Tiempo de carga por sección
- **Error Rates**: Errores por tipo y sección
- **User Adoption**: Métricas de uso
- **Cache Hit Rates**: Eficiencia del cache

**Business Monitoring:**
- **Report Usage**: Informes más/menos usados
- **Export Patterns**: Formatos y frecuencia
- **User Feedback**: Satisfacción y sugerencias

### 3. Maintenance

**Regular Tasks:**
- **Cache Cleanup**: Limpiar cache expirado
- **Log Rotation**: Gestionar logs de aplicación
- **Performance Review**: Análisis mensual de performance
- **Security Updates**: Mantener dependencias actualizadas

## Future Enhancements

### 1. Advanced Features

**Scheduled Reports:**
- Generar informes automáticamente
- Envío por email programado
- Alertas basadas en umbrales

**Collaborative Features:**
- Compartir dashboards personalizados
- Comentarios en informes
- Anotaciones colaborativas

### 2. AI/ML Integration

**Predictive Analytics:**
- Predicción de rotación
- Detección de patrones anómalos
- Recomendaciones automáticas

**Natural Language Queries:**
- Búsqueda en lenguaje natural
- Generación automática de insights
- Chatbot para consultas rápidas

### 3. Mobile App

**Native Mobile:**
- App nativa para iOS/Android
- Notificaciones push
- Acceso offline limitado

**Progressive Web App:**
- PWA para acceso móvil
- Instalación en dispositivos
- Sincronización offline