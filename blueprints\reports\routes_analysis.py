# -*- coding: utf-8 -*-
from flask import render_template, request, redirect, url_for, flash, jsonify
from . import reports_bp
from services.comparative_analysis_service import ComparativeAnalysisService
from services.predictive_analytics_service import PredictiveAnalyticsService
from services.report_service import ReportService
from datetime import datetime
from dateutil.relativedelta import relativedelta
import logging

# Inicializar servicios
comparative_service = ComparativeAnalysisService()
predictive_service = PredictiveAnalyticsService()
report_service = ReportService()

@reports_bp.route('/comparar-periodos')
def compare_periods():
    """Comparar métricas entre períodos"""
    try:
        # Obtener parámetros de la solicitud
        metric = request.args.get('metric', 'absenteeism')
        period1_start = request.args.get('period1_start')
        period1_end = request.args.get('period1_end')
        period2_start = request.args.get('period2_start')
        period2_end = request.args.get('period2_end')
        department_id = request.args.get('department_id', type=int)
        
        # Convertir fechas si se proporcionan
        if period1_start:
            period1_start = datetime.strptime(period1_start, '%Y-%m-%d').date()
        if period1_end:
            period1_end = datetime.strptime(period1_end, '%Y-%m-%d').date()
        if period2_start:
            period2_start = datetime.strptime(period2_start, '%Y-%m-%d').date()
        if period2_end:
            period2_end = datetime.strptime(period2_end, '%Y-%m-%d').date()
        
        # Realizar comparación
        comparison_result = comparative_service.compare_periods(
            metric=metric,
            period1_start=period1_start,
            period1_end=period1_end,
            period2_start=period2_start,
            period2_end=period2_end,
            department_id=department_id
        )
        
        # Obtener departamentos para el filtro
        from models import Departamento
        departments = Departamento.query.all()
        
        return render_template('reports/compare_periods.html',
                             comparison_result=comparison_result,
                             departments=departments,
                             metric=metric)
        
    except Exception as e:
        logging.error(f"Error en compare_periods: {str(e)}")
        flash('Error al realizar la comparación de períodos', 'error')
        return redirect(url_for('reports.index'))

@reports_bp.route('/comparar-departamentos')
def compare_departments():
    """Comparar métricas entre departamentos"""
    try:
        # Obtener parámetros de la solicitud
        metric = request.args.get('metric', 'absenteeism')
        period_start = request.args.get('period_start')
        period_end = request.args.get('period_end')
        
        # Convertir fechas si se proporcionan
        if period_start:
            period_start = datetime.strptime(period_start, '%Y-%m-%d').date()
        if period_end:
            period_end = datetime.strptime(period_end, '%Y-%m-%d').date()
        
        # Realizar comparación
        comparison_result = comparative_service.compare_departments(
            metric=metric,
            period_start=period_start,
            period_end=period_end
        )
        
        return render_template('reports/compare_departments.html',
                             comparison_result=comparison_result,
                             metric=metric)
        
    except Exception as e:
        logging.error(f"Error en compare_departments: {str(e)}")
        flash('Error al realizar la comparación entre departamentos', 'error')
        return redirect(url_for('reports.index'))

@reports_bp.route('/analisis-benchmark')
def benchmark_analysis():
    """Análisis de benchmarking"""
    try:
        # Obtener parámetro de la solicitud
        metric = request.args.get('metric', 'absenteeism')
        
        # Realizar análisis de benchmarking
        benchmark_result = comparative_service.benchmark_analysis(metric=metric)
        
        return render_template('reports/benchmark_analysis.html',
                             benchmark_result=benchmark_result,
                             metric=metric)
        
    except Exception as e:
        logging.error(f"Error en benchmark_analysis: {str(e)}")
        flash('Error al realizar el análisis de benchmarking', 'error')
        return redirect(url_for('reports.index'))

@reports_bp.route('/analisis-predictivo')
def predictive_analysis():
    """Análisis predictivo"""
    try:
        # Obtener parámetros de la solicitud
        analysis_type = request.args.get('type', 'comprehensive')
        months_ahead = request.args.get('months', type=int, default=6)
        
        # Generar informe predictivo
        predictive_result = predictive_service.generate_predictive_report(
            report_type=analysis_type,
            months_ahead=months_ahead
        )
        
        return render_template('reports/predictive_analysis.html',
                             predictive_result=predictive_result,
                             analysis_type=analysis_type,
                             months_ahead=months_ahead)
        
    except Exception as e:
        logging.error(f"Error en predictive_analysis: {str(e)}")
        flash('Error al realizar el análisis predictivo', 'error')
        return redirect(url_for('reports.index'))