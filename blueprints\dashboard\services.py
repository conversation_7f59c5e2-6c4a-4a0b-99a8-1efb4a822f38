# -*- coding: utf-8 -*-
from models import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Depar<PERSON><PERSON><PERSON>
from sqlalchemy import func
from database import db
from datetime import datetime, timedelta
from services.history_service import history_service
from services.activity_formatter import activity_formatter
from services.employee_service import EmployeeService
from services.statistics_service import statistics_service

# Instanciar servicios
employee_service = EmployeeService()

class DashboardService:
    @staticmethod
    def get_kpis():
        """Obtiene todos los KPIs para el dashboard"""
        try:
            # Calcular KPIs
            kpis = {
                # Total de empleados se refiere solo a los empleados activos
                'total_empleados': employee_service.count_active_employees(),
                # Empleados activos son los empleados activos menos los que tienen permisos vigentes
                'empleados_activos': employee_service.count_truly_active_employees(),
                'evaluaciones_pendientes': 0,  # Se calculará más abajo
                'total_evaluaciones': 0,  # Se calculará más abajo
                'permisos_pendientes': Permiso.query.filter_by(estado='Pendiente').count(),
                'permisos_mes': Permiso.query.filter(
                    Permiso.fecha_inicio >= datetime.now().date().replace(day=1)
                ).count(),
                'promedio_evaluacion': 0,  # Se calculará más abajo
            }

            # --- Alertas rápidas ---
            # Contratos por vencer en los próximos 30 días
            try:
                hoy = datetime.now().date()
                en_30_dias = hoy + timedelta(days=30)
                contratos_por_vencer = Empleado.query.filter(
                    Empleado.fecha_finalizacion != None,
                    Empleado.fecha_finalizacion >= hoy,
                    Empleado.fecha_finalizacion <= en_30_dias
                ).count()
            except Exception:
                contratos_por_vencer = 0
            kpis['contratos_por_vencer'] = contratos_por_vencer

            # Permisos urgentes (estado 'Urgente' o similar)
            try:
                permisos_urgentes = Permiso.query.filter(
                    (Permiso.estado == 'Urgente') | (Permiso.prioridad == 'Alta')
                ).count()
            except Exception:
                permisos_urgentes = 0
            kpis['permisos_urgentes'] = permisos_urgentes

            # Incidencias abiertas (si existe modelo Incidencia)
            try:
                from models import Incidencia
                incidencias_abiertas = Incidencia.query.filter_by(estado='Abierta').count()
            except Exception:
                incidencias_abiertas = 0
            kpis['incidencias_abiertas'] = incidencias_abiertas

            # Datos para gráfico de departamentos
            # Solo considerar empleados activos
            dept_data = db.session.query(
                Departamento.nombre,
                func.count(Empleado.id)
            ).join(Empleado).filter(Empleado.activo == True).group_by(Departamento.nombre).all()

            kpis['dept_labels'] = [d[0] for d in dept_data]
            kpis['dept_data'] = [d[1] for d in dept_data]

            # Calcular evaluaciones usando el nuevo sistema rediseñado
            try:
                from models_evaluacion import EvaluacionEmpleado
                empleados = employee_service.get_all_active_employees()
                evaluaciones_pendientes = 0
                total_evaluaciones = 0
                suma_promedios = 0
                contador_promedios = 0
                
                # Verificar si hay datos en la tabla de evaluaciones
                todas_evaluaciones = EvaluacionEmpleado.query.all()
                total_evaluaciones = len(todas_evaluaciones)
                
                # Contar empleados que necesitan evaluación
                for empleado in empleados:
                    # Obtener evaluaciones del empleado
                    evaluaciones_empleado = EvaluacionEmpleado.query.filter_by(empleado_id=empleado.id).all()
                    
                    # Calcular promedio de evaluaciones del empleado
                    if evaluaciones_empleado:
                        for eval_emp in evaluaciones_empleado:
                            if eval_emp.respuestas:
                                promedio_eval = sum(r.puntuacion for r in eval_emp.respuestas) / len(eval_emp.respuestas)
                                suma_promedios += promedio_eval
                                contador_promedios += 1
                    
                    # Verificar si necesita evaluación (última evaluación hace más de 6 meses o no tiene evaluación)
                    ultima_evaluacion = EvaluacionEmpleado.query.filter_by(empleado_id=empleado.id)\
                        .order_by(EvaluacionEmpleado.fecha.desc())\
                        .first()
                    
                    fecha_actual = datetime.now().date()
                    fecha_limite = fecha_actual - timedelta(days=180)
                    
                    if not ultima_evaluacion:
                        # No tiene ninguna evaluación
                        evaluaciones_pendientes += 1
                    elif ultima_evaluacion.fecha and ultima_evaluacion.fecha.date() < fecha_limite:
                        # La última evaluación fue hace más de 6 meses
                        evaluaciones_pendientes += 1
                
                kpis['evaluaciones_pendientes'] = evaluaciones_pendientes
                kpis['total_evaluaciones'] = total_evaluaciones
                kpis['promedio_evaluacion'] = round(suma_promedios / contador_promedios, 2) if contador_promedios > 0 else 0
                
            except Exception as e:
                import logging
                logging.error(f"Error calculando evaluaciones: {str(e)}")
                # Si hay error con el nuevo sistema, usar valores por defecto
                kpis['evaluaciones_pendientes'] = 0
                kpis['total_evaluaciones'] = 0
                kpis['promedio_evaluacion'] = 0

            # Datos para gráfico de evaluaciones (usando el nuevo sistema)
            try:
                from models_evaluacion import EvaluacionEmpleado
                last_90_days = datetime.now() - timedelta(days=90)
                
                # Obtener evaluaciones de los últimos 90 días
                evaluaciones_recientes = EvaluacionEmpleado.query.filter(
                    EvaluacionEmpleado.fecha >= last_90_days
                ).all()
                
                # Solo mostrar datos si hay evaluaciones reales
                if evaluaciones_recientes:
                    # Agrupar por fecha y calcular promedio
                    eval_data = {}
                    for eval_emp in evaluaciones_recientes:
                        if eval_emp.fecha and eval_emp.respuestas:
                            fecha_str = eval_emp.fecha.strftime('%Y-%m-%d')
                            promedio = sum(r.puntuacion for r in eval_emp.respuestas) / len(eval_emp.respuestas)
                            
                            if fecha_str in eval_data:
                                eval_data[fecha_str].append(promedio)
                            else:
                                eval_data[fecha_str] = [promedio]
                    
                    # Calcular promedio por fecha
                    fechas_ordenadas = sorted(eval_data.keys())
                    kpis['eval_labels'] = [fecha for fecha in fechas_ordenadas]
                    kpis['eval_data'] = [round(sum(eval_data[fecha]) / len(eval_data[fecha]), 2) for fecha in fechas_ordenadas]
                else:
                    # No mostrar datos de relleno si no hay evaluaciones reales
                    kpis['eval_labels'] = []
                    kpis['eval_data'] = []
                
            except Exception as e:
                import logging
                logging.error(f"Error calculando gráfico de evaluaciones: {str(e)}")
                kpis['eval_labels'] = []
                kpis['eval_data'] = []

            # Actividad reciente usando el servicio de historial (limitado a 5 registros)
            actividad_reciente = history_service.get_recent_activity(5)

            # Formatear las descripciones para hacerlas más amigables
            for cambio in actividad_reciente:
                cambio.formatted_description = activity_formatter.format_activity_description(cambio)

            kpis['actividad_reciente'] = actividad_reciente

            # Añadir evolución de empleados activos por mes
            etiquetas_meses = []
            empleados_por_mes = []
            try:
                # Obtener datos históricos de plantilla
                fecha_actual = datetime.now().date()
                for i in range(12):
                    fecha_mes = fecha_actual - timedelta(days=30*(11-i))
                    nombres_meses = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic']
                    etiqueta_mes = f"{nombres_meses[fecha_mes.month-1]} {fecha_mes.year}"
                    etiquetas_meses.append(etiqueta_mes)
                # Usar el método real del statistics_service
                empleados_por_mes = statistics_service.get_workforce_evolution()
            except Exception:
                # Si hay error, usar datos de ejemplo
                etiquetas_meses = [f"Mes {i+1}" for i in range(12)]
                empleados_por_mes = [80 + i*2 for i in range(12)]
            kpis['evolucion_meses'] = etiquetas_meses
            kpis['evolucion_empleados'] = empleados_por_mes

            return kpis

        except Exception as e:
            import logging
            logging.error(f"Error loading dashboard data: {str(e)}")
            # Provide default values in case of error
            return {
                'total_empleados': 0,
                'empleados_activos': 0,
                'evaluaciones_pendientes': 0,
                'total_evaluaciones': 0,
                'permisos_pendientes': 0,
                'permisos_mes': 0,
                'promedio_evaluacion': 0,
                'dept_labels': [],
                'dept_data': [],
                'eval_labels': [],
                'eval_data': [],
                'actividad_reciente': [],
                'evolucion_meses': [],
                'evolucion_empleados': []
            }

    @staticmethod
    def get_recent_activity(limit=50):
        """Obtiene la actividad reciente para la página de actividad"""
        try:
            # Obtener actividad reciente
            actividad = history_service.get_recent_activity(limit)

            # Formatear las descripciones para hacerlas más amigables
            for cambio in actividad:
                cambio.formatted_description = activity_formatter.format_activity_description(cambio)

            return actividad
        except Exception as e:
            import logging
            logging.error(f"Error loading activity data: {str(e)}")
            return []
