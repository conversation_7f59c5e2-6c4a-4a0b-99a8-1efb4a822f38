# -*- coding: utf-8 -*-
import os
import sys
from datetime import timedelta

class Config:
    SECRET_KEY = os.getenv('SECRET_KEY', 'default_secret_key')
    
    # Asegurar que el directorio app_data existe
    APP_DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'app_data')
    if not os.path.exists(APP_DATA_DIR):
        os.makedirs(APP_DATA_DIR)
    
    # Configurar ruta de base de datos según el entorno
    if getattr(sys, 'frozen', False):
        # Estamos ejecutando desde un bundle de PyInstaller
        application_path = os.path.dirname(sys.executable)
        SQLALCHEMY_DATABASE_URI = f'sqlite:///{os.path.join(application_path, "db.sqlite3")}'
    else:
        # Estamos ejecutando desde el código fuente
        SQLALCHEMY_DATABASE_URI = f'sqlite:///{os.path.join(APP_DATA_DIR, "unified_app.db")}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Cache configuration
    CACHE_TYPE = 'redis'
    CACHE_REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
    CACHE_DEFAULT_TIMEOUT = 300

    # Upload configuration
    UPLOAD_FOLDER = 'uploads'
    BACKUP_FOLDER = 'backups'
    REPORTS_DIR = os.path.join(UPLOAD_FOLDER, 'reports')

    # Session configuration
    PERMANENT_SESSION_LIFETIME = timedelta(days=1)

    # Logging configuration
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_FILE = 'app.log'
    LOG_MAX_BYTES = 1024 * 1024  # 1MB
    LOG_BACKUP_COUNT = 3

class DevelopmentConfig(Config):
    DEBUG = True
    TESTING = False

class TestingConfig(Config):
    DEBUG = False
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

class ProductionConfig(Config):
    DEBUG = False
    TESTING = False
    SECRET_KEY = os.getenv('SECRET_KEY')

config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
