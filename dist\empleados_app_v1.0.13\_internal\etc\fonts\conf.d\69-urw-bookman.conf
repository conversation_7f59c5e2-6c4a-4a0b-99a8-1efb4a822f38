<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">
<fontconfig>
  <!-- Generic name aliasing -->
  <alias>
    <family>serif</family>
    <prefer>
      <family>URW Bookman</family>
    </prefer>
  </alias>

  <!-- Generic name assignment -->
  <alias>
    <family>URW Bookman</family>
    <default>
      <family>serif</family>
    </default>
  </alias>

  <!-- Original PostScript base font mapping -->
  <alias binding="same">
    <family>URW Bookman</family>
    <default>
      <family>ITC Bookman</family>
    </default>
  </alias>

  <!-- Font substitution rules -->
  <alias binding="same">
    <family>ITC Bookman</family>
    <accept>
      <family>URW Bookman</family>
    </accept>
  </alias>

  <alias binding="same">
    <family>Bookman Old Style</family>
    <accept>
      <family>URW Bookman</family>
    </accept>
  </alias>

  <alias binding="same">
    <family>TeX Gyre Bonum</family>
    <accept>
      <family>UR<PERSON> Bookman</family>
    </accept>
  </alias>
</fontconfig>
