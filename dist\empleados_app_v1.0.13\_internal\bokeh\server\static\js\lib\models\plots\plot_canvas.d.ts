import { CanvasPanel } from "../canvas/canvas_panel";
import type { CartesianFrameView } from "../canvas/cartesian_frame";
import type { CanvasView } from "../canvas/canvas";
import type { Renderer } from "../renderers/renderer";
import { RendererView } from "../renderers/renderer";
import type { DataRenderer } from "../renderers/data_renderer";
import type { Range } from "../ranges/range";
import type { Tool } from "../tools/tool";
import type { Selection } from "../selections/selection";
import type { LayoutDOM, DOMBoxSizing, FullDisplay } from "../layouts/layout_dom";
import { LayoutDOMView } from "../layouts/layout_dom";
import type { Plot } from "./plot";
import { Title } from "../annotations/title";
import { AxisView } from "../axes/axis";
import type { ToolbarPanelView } from "../annotations/toolbar_panel";
import { ToolbarPanel } from "../annotations/toolbar_panel";
import type { AutoRanged } from "../ranges/data_range1d";
import type { Menu } from "../ui/menus/menu";
import type { ElementLike } from "../ui/pane";
import { Panel } from "../ui/panel";
import type { ViewStorage, IterViews, ViewOf, BuildResult } from "../../core/build_views";
import type { Paintable } from "../../core/visuals";
import type { RenderLevel } from "../../core/enums";
import type { View } from "../../core/view";
import { Signal0 } from "../../core/signaling";
import type { Context2d } from "../../core/util/canvas";
import { CanvasLayer } from "../../core/util/canvas";
import type { Layoutable } from "../../core/layout";
import { BorderLayout } from "../../core/layout/border";
import { BBox } from "../../core/util/bbox";
import type { XY } from "../../core/util/bbox";
import type { RangeInfo, RangeOptions } from "./range_manager";
import { RangeManager } from "./range_manager";
import type { StateInfo } from "./state_manager";
import { StateManager } from "./state_manager";
import type { StyleSheetLike } from "../../core/dom";
import { InlineStyleSheet } from "../../core/dom";
import type { XY as XY_ } from "../coordinates/xy";
import type { Indexed } from "../coordinates/indexed";
export declare class PlotView extends LayoutDOMView implements Paintable {
    model: Plot;
    visuals: Plot.Visuals;
    layout: BorderLayout;
    private _top_panel;
    private _bottom_panel;
    private _left_panel;
    private _right_panel;
    top_panel: ViewOf<CanvasPanel>;
    bottom_panel: ViewOf<CanvasPanel>;
    left_panel: ViewOf<CanvasPanel>;
    right_panel: ViewOf<CanvasPanel>;
    private _inner_top_panel;
    private _inner_bottom_panel;
    private _inner_left_panel;
    private _inner_right_panel;
    inner_top_panel: ViewOf<CanvasPanel>;
    inner_bottom_panel: ViewOf<CanvasPanel>;
    inner_left_panel: ViewOf<CanvasPanel>;
    inner_right_panel: ViewOf<CanvasPanel>;
    private _frame;
    frame_view: CartesianFrameView;
    get frame(): CartesianFrameView;
    private _canvas;
    canvas_view: CanvasView;
    get canvas(): CanvasView;
    private _render_count;
    readonly repainted: Signal0<this>;
    protected readonly _computed_style: InlineStyleSheet;
    stylesheets(): StyleSheetLike[];
    protected _title?: Title;
    protected _toolbar?: ToolbarPanel;
    protected _attribution: Panel;
    protected _notifications: Panel;
    get toolbar_panel(): ToolbarPanelView | null;
    protected _inner_bbox: BBox;
    protected _needs_paint: boolean;
    protected _invalidated_painters: Set<RendererView>;
    protected _invalidate_all: boolean;
    protected _state_manager: StateManager;
    protected _range_manager: RangeManager;
    get state(): StateManager;
    set invalidate_dataranges(value: boolean);
    protected lod_started: boolean;
    protected _initial_state: StateInfo;
    protected throttled_paint: () => Promise<void>;
    computed_renderers: Renderer[];
    get computed_renderer_views(): RendererView[];
    get all_renderer_views(): RendererView[];
    get auto_ranged_renderers(): (RendererView & AutoRanged)[];
    get base_font_size(): number | null;
    readonly renderer_views: ViewStorage<Renderer>;
    readonly tool_views: ViewStorage<Tool>;
    children(): IterViews;
    get child_models(): LayoutDOM[];
    private _is_paused;
    get is_paused(): boolean;
    pause(): void;
    unpause(no_render?: boolean): void;
    private _needs_notify;
    notify_finished_after_paint(): void;
    request_repaint(): void;
    request_paint(...to_invalidate: (Renderer | RendererView)[]): void;
    invalidate_painters(...to_invalidate: (Renderer | RendererView)[]): void;
    schedule_paint(): void;
    request_layout(): void;
    reset(): void;
    remove(): void;
    protected _provide_context_menu(): Menu | null;
    get_context_menu(xy: XY): ViewOf<Menu> | null;
    initialize(): void;
    get elements(): ElementLike[];
    lazy_initialize(): Promise<void>;
    box_sizing(): DOMBoxSizing;
    protected _intrinsic_display(): FullDisplay;
    private _compute_layout_panels;
    _update_layout(): void;
    protected _measure_layout(): void;
    get axis_views(): AxisView[];
    update_range(range_info: RangeInfo, options?: Partial<RangeOptions>): void;
    reset_range(): void;
    trigger_ranges_update_event(extra_ranges?: Range[]): void;
    get_selection(): Map<DataRenderer, Selection>;
    update_selection(selections: Map<DataRenderer, Selection> | null): void;
    reset_selection(): void;
    protected _invalidate_layout_if_needed(): void;
    protected _compute_renderers(): Generator<Renderer, void, undefined>;
    protected _update_attribution(): void;
    protected _build_renderers(): Promise<BuildResult<Renderer>>;
    protected _update_renderers(): Promise<void>;
    build_renderer_views(): Promise<void>;
    build_tool_views(): Promise<void>;
    connect_signals(): void;
    protected _update_touch_action(): void;
    has_finished(): boolean;
    _after_layout(): void;
    render(): void;
    repaint(): void;
    paint(): void;
    protected _actual_paint(): void;
    protected _paint_primary(ctx: Context2d): void;
    protected _paint_overlays(ctx: Context2d): void;
    protected _paint_levels(ctx: Context2d, level: RenderLevel, clip_box: BBox, global_clip: boolean): void;
    paint_layout(ctx: Context2d, layout: Layoutable): void;
    protected _paint_empty(ctx: Context2d, frame_box: BBox): void;
    protected _paint_outline(ctx: Context2d, frame_box: BBox): void;
    private _force_paint;
    get is_forcing_paint(): boolean;
    force_paint(fn: () => void): void;
    export(type?: "auto" | "png" | "svg", hidpi?: boolean): CanvasLayer;
    resolve_frame(): View | null;
    resolve_canvas(): View | null;
    resolve_plot(): View | null;
    resolve_xy(coord: XY_): XY;
    resolve_indexed(coord: Indexed): XY;
    protected _messages: Map<string, number>;
    notify_about(message: string): void;
    serializable_children(): View[];
}
//# sourceMappingURL=plot_canvas.d.ts.map