<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "urn:fontconfig:fonts.dtd">
<fontconfig>
  <description>Set preferable fonts for emoji/math fonts</description>
<!-- Keep in sync with 45-generic.conf -->

<!-- Emoji -->

	<!-- Prefer to match color emoji font. -->
	<match>
		<test name="lang">
			<string>und-zsye</string>
		</test>
		<test qual="all" name="color" compare="not_eq">
			<bool>true</bool>
		</test>
		<test qual="all" name="color" compare="not_eq">
			<bool>false</bool>
		</test>
		<edit name="color" mode="append">
			<bool>true</bool>
		</edit>
	</match>

	<!-- TODO
	 ! Match on "color" and alias B&W ones first if no color is requested.
	 ! That's "hard" because <alias> doesn't work in match and needs to be
	 ! expanded to its non-sugar form.
	 !-->
	<alias binding="same">
		<family>emoji</family>
		<prefer>
			<!-- System fonts -->
			<family>Noto Color Emoji</family> <!-- Google -->
			<family>Apple Color Emoji</family> <!-- Apple -->
			<family>Segoe UI Emoji</family> <!-- Microsoft -->
			<family>Twitter Color Emoji</family> <!-- Twitter -->
			<family>EmojiOne Mozilla</family> <!-- Mozilla -->
			<!-- Third-Party fonts -->
			<family>Emoji Two</family>
			<family>JoyPixels</family>
			<family>Emoji One</family>
			<!-- Non-color -->
			<family>Noto Emoji</family> <!-- Google -->
			<family>Android Emoji</family> <!-- Google -->
		</prefer>
	</alias>

<!-- Math -->
	<alias binding="same">
		<!-- https://en.wikipedia.org/wiki/Category:Mathematical_OpenType_typefaces -->
		<family>math</family>
		<prefer>
			<family>XITS Math</family> <!-- Khaled Hosny -->
			<family>STIX Two Math</family> <!-- AMS -->
			<family>Cambria Math</family> <!-- Microsoft -->
			<family>Latin Modern Math</family> <!-- TeX -->
			<family>Minion Math</family> <!-- Adobe -->
			<family>Lucida Math</family> <!-- Adobe -->
			<family>Asana Math</family>
		</prefer>
	</alias>

</fontconfig>
