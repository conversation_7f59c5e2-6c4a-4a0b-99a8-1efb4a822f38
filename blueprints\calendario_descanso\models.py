from flask_sqlalchemy import SQLAlchemy
from datetime import date

db = SQLAlchemy()

class Descanso(db.Model):
    __tablename__ = 'descansos'
    id = db.Column(db.Integer, primary_key=True)
    empleado_id = db.Column(db.Integer, nullable=False)
    sector_id = db.Column(db.Integer, nullable=False)
    fecha = db.Column(db.Date, nullable=False)
    es_descanso = db.Column(db.<PERSON><PERSON>, default=False)
    observacion = db.Column(db.String(255))
    creado_por = db.Column(db.String(64))
    creado_en = db.Column(db.DateTime, default=db.func.now()) 