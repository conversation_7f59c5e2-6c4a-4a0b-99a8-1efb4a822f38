# -*- coding: utf-8 -*-
from flask import render_template, request, jsonify, flash, redirect, url_for
from . import analytics_bp
from models import Departamento, Sector
from models_polivalencia import NIVELES_POLIVALENCIA
from database import db
import logging

@analytics_bp.route('/')
def index():
    """Página principal de análisis estadístico avanzado"""
    try:
        # Obtener departamentos para el filtro
        departamentos = Departamento.query.all()

        return render_template('analytics/index.html',
                             departamentos=departamentos,
                             title="Análisis Estadístico Avanzado")
    except Exception as e:
        logging.error(f"Error en la página de análisis estadístico: {str(e)}")
        flash(f"Error al cargar la página de análisis: {str(e)}", "error")
        return redirect(url_for('dashboard.index'))

@analytics_bp.route('/performance_trends')
def performance_trends():
    """Redirigir a la ruta correspondiente en el blueprint de estadísticas"""
    return redirect(url_for('statistics.polivalencia_evolution'))

@analytics_bp.route('/competency_clusters')
def competency_clusters():
    """Redirigir a la ruta correspondiente en el blueprint de estadísticas"""
    return redirect(url_for('statistics.competence_seniority'))

@analytics_bp.route('/polivalencia_patterns')
def polivalencia_patterns():
    """Redirigir a la ruta correspondiente en el blueprint de estadísticas"""
    return redirect(url_for('statistics.coverage_dashboard'))

@analytics_bp.route('/absenteeism_correlation')
def absenteeism_correlation():
    """Redirigir a la ruta correspondiente en el blueprint de estadísticas"""
    return redirect(url_for('statistics.absenteeism_impact'))