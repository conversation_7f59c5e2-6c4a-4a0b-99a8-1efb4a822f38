#-----------------------------------------------------------------------------
# Copyright (c) Anaconda, Inc., and Bokeh Contributors.
# All rights reserved.
#
# The full license is in the file LICENSE.txt, distributed with this software.
#-----------------------------------------------------------------------------

# Standard library imports
from dataclasses import dataclass
from datetime import date, datetime
from typing import Literal, Sequence

# Bokeh imports
from ..._types import Color, Datetime, Readonly
from ...core.has_props import abstract
from ..formatters import TickFormatter
from .widget import Widget

@abstract
@dataclass(init=False)
class AbstractSlider(Widget):

    orientation: Literal["horizontal", "vertical"] = ...

    title: str | None = ...

    show_value: bool = ...

    direction: Literal["ltr", "rtl"] = ...

    tooltips: bool = ...

    bar_color: Color = ...

@abstract
@dataclass(init=False)
class NumericalSlider(AbstractSlider):

    format: str | TickFormatter = ...

@dataclass
class CategoricalSlider(AbstractSlider):

    categories: Sequence[str] = ...

    value: str = ...

    value_throttled: Readonly[str] = ...

@dataclass
class Slider(NumericalSlider):

    start: float = ...

    end: float = ...

    value: float = ...

    value_throttled: Readonly[float] = ...

    step: float = ...

@dataclass
class RangeSlider(NumericalSlider):

    value: tuple[float, float] = ...

    value_throttled: Readonly[tuple[float, float]] = ...

    start: float = ...

    end: float = ...

    step: float = ...

@dataclass
class DateSlider(NumericalSlider):

    @property
    def value_as_datetime(self) -> datetime | None: ...

    @property
    def value_as_date(self) -> date | None: ...

    value: Datetime = ...

    value_throttled: Readonly[Datetime] = ...

    start: Datetime = ...

    end: Datetime = ...

    step: int = ...

@dataclass
class DateRangeSlider(NumericalSlider):

    @property
    def value_as_datetime(self) -> tuple[datetime, datetime] | None: ...

    @property
    def value_as_date(self) -> tuple[date, date] | None: ...

    value: tuple[Datetime, Datetime] = ...

    value_throttled: Readonly[tuple[Datetime, Datetime]] = ...

    start: Datetime = ...

    end: Datetime = ...

    step: int = ...

@dataclass
class DatetimeRangeSlider(NumericalSlider):

    @property
    def value_as_datetime(self) -> tuple[datetime, datetime] | None: ...

    value: tuple[Datetime, Datetime] = ...

    value_throttled: Readonly[tuple[Datetime, Datetime]] = ...

    start: Datetime = ...

    end: Datetime = ...

    step: int = ...
