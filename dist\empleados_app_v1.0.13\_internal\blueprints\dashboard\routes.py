# -*- coding: utf-8 -*-
from flask import render_template, current_app, flash, redirect, url_for, request, jsonify
from . import dashboard_bp
from .services import DashboardService
import sys
import os
import logging
from datetime import datetime
from dateutil.relativedelta import relativedelta
from sqlalchemy import func

@dashboard_bp.route('/')
def index():
    """Página principal del dashboard"""
    kpis = DashboardService.get_kpis()

    # Usar siempre la versión original
    template = 'index.html'

    return render_template(template,
                         kpis=kpis,
                         version=current_app.config.get('VERSION', '1.0.0'))

@dashboard_bp.route('/actividad')
def ver_actividad():
    """Ver toda la actividad reciente"""
    actividad = DashboardService.get_recent_activity(50)
    return render_template('actividad.html',
                         actividad=actividad,
                         title="Actividad Reciente")

@dashboard_bp.route('/actualizar-historial-permisos')
def actualizar_historial_permisos():
    """Actualiza retroactivamente las descripciones de historial de permisos"""
    try:
        # Importar el script de actualización
        sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))
        from scripts.actualizar_historial_permisos import actualizar_historial_permisos as actualizar

        # Ejecutar la actualización
        exito, mensaje = actualizar()

        if exito:
            flash(mensaje, "success")
        else:
            flash(mensaje, "error")

    except Exception as e:
        logging.error(f"Error al actualizar el historial de permisos: {str(e)}")
        flash(f"Error al actualizar el historial de permisos: {str(e)}", "error")

    return redirect(url_for('dashboard.ver_actividad'))

@dashboard_bp.route('/api/evolucion_empleados')
def api_evolucion_empleados():
    """Devuelve la evolución de empleados activos por mes, filtrando por fechas y departamento (nombre)"""
    from services.statistics_service import statistics_service
    from models import Empleado, Departamento

    # Obtener parámetros
    fecha_inicio = request.args.get('fecha_inicio')
    fecha_fin = request.args.get('fecha_fin')
    departamento = request.args.get('departamento')

    # Validar fechas
    try:
        if fecha_inicio:
            fecha_inicio = datetime.strptime(fecha_inicio, '%Y-%m-%d').date().replace(day=1)
        if fecha_fin:
            fecha_fin = datetime.strptime(fecha_fin, '%Y-%m-%d').date().replace(day=1)
    except Exception:
        return jsonify({'error': 'Formato de fecha inválido'}), 400

    # Determinar rango de meses
    if not fecha_inicio or not fecha_fin or fecha_fin < fecha_inicio:
        # Por defecto, últimos 12 meses
        fecha_fin = datetime.now().date().replace(day=1)
        fecha_inicio = fecha_fin - relativedelta(months=11)

    meses = []
    labels = []
    actual = fecha_inicio
    nombres_meses = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic']
    while actual <= fecha_fin:
        meses.append(actual)
        labels.append(f"{nombres_meses[actual.month-1]} {actual.year}")
        actual += relativedelta(months=1)

    # Filtro de departamento
    dept_id = None
    if departamento:
        dept = Departamento.query.filter_by(nombre=departamento).first()
        if dept:
            dept_id = dept.id

    # Calcular empleados activos por mes
    data = []
    for mes in meses:
        query = Empleado.query.filter(
            Empleado.fecha_ingreso <= mes,
            (Empleado.fecha_finalizacion == None) | (Empleado.fecha_finalizacion > mes)
        )
        if dept_id:
            query = query.filter(Empleado.departamento_id == dept_id)
        data.append(query.count())

    return jsonify({'labels': labels, 'data': data})

@dashboard_bp.route('/api/distribucion_departamentos')
def api_distribucion_departamentos():
    from models import Empleado, Departamento
    from flask import request, jsonify
    # Filtro de tipo de empleado
    tipo = request.args.get('tipo', 'todos')
    query = Empleado.query
    if tipo == 'activos':
        query = query.filter(Empleado.activo == True)
    elif tipo == 'inactivos':
        query = query.filter(Empleado.activo == False)
    # Obtener departamentos y conteo
    dept_data = (
        query.join(Departamento, Empleado.departamento_id == Departamento.id)
        .with_entities(Departamento.nombre, func.count(Empleado.id))
        .group_by(Departamento.nombre)
        .order_by(func.count(Empleado.id).desc())
        .all()
    )
    labels = [d[0] for d in dept_data]
    data = [d[1] for d in dept_data]
    return jsonify({'labels': labels, 'data': data})
