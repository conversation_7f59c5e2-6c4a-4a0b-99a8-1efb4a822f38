# -*- coding: utf-8 -*-
"""
Rutas para la gestión de usuarios
"""
import json
from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from werkzeug.security import generate_password_hash
from datetime import datetime

from models import db, Usuario

users_bp = Blueprint('users', __name__, url_prefix='/users')

@users_bp.route('/')
@login_required
def index():
    """
    Lista de usuarios
    """
    # Verificar si el usuario actual es administrador
    if current_user.rol != 'admin':
        flash('No tiene permisos para acceder a esta sección', 'danger')
        return redirect(url_for('dashboard.index'))
    
    # Obtener parámetros de filtro
    rol = request.args.get('rol', '')
    estado = request.args.get('estado', '')
    
    # Construir la consulta
    query = Usuario.query
    
    if rol:
        query = query.filter(Usuario.rol == rol)
    
    if estado == 'activo':
        query = query.filter(Usuario.activo == True)
    elif estado == 'inactivo':
        query = query.filter(Usuario.activo == False)
    
    # Ejecutar la consulta
    usuarios = query.order_by(Usuario.id).all()
    
    return render_template('users/index.html', 
                          usuarios=usuarios, 
                          rol_filtro=rol, 
                          estado_filtro=estado)

@users_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """
    Crear un nuevo usuario
    """
    # Verificar si el usuario actual es administrador
    if current_user.rol != 'admin':
        flash('No tiene permisos para acceder a esta sección', 'danger')
        return redirect(url_for('dashboard.index'))
    
    if request.method == 'POST':
        nombre = request.form.get('nombre')
        email = request.form.get('email')
        password = request.form.get('password')
        rol = request.form.get('rol')
        activo = 'activo' in request.form
        
        # Validar datos
        if not nombre or not email or not password or not rol:
            flash('Todos los campos son obligatorios', 'danger')
            return render_template('users/create.html')
        
        # Verificar si el email ya existe
        if Usuario.query.filter_by(email=email).first():
            flash('El email ya está registrado', 'danger')
            return render_template('users/create.html')
        
        try:
            # Crear el usuario
            nuevo_usuario = Usuario(
                nombre=nombre,
                email=email,
                password_hash=generate_password_hash(password),
                rol=rol,
                activo=activo,
                fecha_creacion=datetime.now()
            )
            
            db.session.add(nuevo_usuario)
            db.session.commit()
            
            flash('Usuario creado correctamente', 'success')
            return redirect(url_for('users.index'))
        except Exception as e:
            db.session.rollback()
            flash(f'Error al crear el usuario: {str(e)}', 'danger')
    
    return render_template('users/create.html')

@users_bp.route('/edit/<int:user_id>', methods=['GET', 'POST'])
@login_required
def edit(user_id):
    """
    Editar un usuario existente
    """
    # Verificar si el usuario actual es administrador
    if current_user.rol != 'admin':
        flash('No tiene permisos para acceder a esta sección', 'danger')
        return redirect(url_for('dashboard.index'))
    
    # Obtener el usuario
    usuario = Usuario.query.get_or_404(user_id)
    
    if request.method == 'POST':
        nombre = request.form.get('nombre')
        email = request.form.get('email')
        password = request.form.get('password')
        rol = request.form.get('rol')
        activo = 'activo' in request.form
        
        # Validar datos
        if not nombre or not email or not rol:
            flash('Nombre, email y rol son obligatorios', 'danger')
            return render_template('users/edit.html', usuario=usuario)
        
        # Verificar si el email ya existe (excepto para el usuario actual)
        email_existente = Usuario.query.filter(Usuario.email == email, Usuario.id != user_id).first()
        if email_existente:
            flash('El email ya está registrado por otro usuario', 'danger')
            return render_template('users/edit.html', usuario=usuario)
        
        try:
            # No permitir desactivar el último administrador
            if usuario.rol == 'admin' and not activo:
                admin_count = Usuario.query.filter_by(rol='admin', activo=True).count()
                if admin_count <= 1:
                    flash('No se puede desactivar el último administrador activo', 'danger')
                    return render_template('users/edit.html', usuario=usuario)
            
            # Actualizar el usuario
            usuario.nombre = nombre
            usuario.email = email
            usuario.rol = rol
            usuario.activo = activo
            
            # Actualizar contraseña solo si se proporciona una nueva
            if password:
                usuario.password_hash = generate_password_hash(password)
            
            db.session.commit()
            
            flash('Usuario actualizado correctamente', 'success')
            return redirect(url_for('users.index'))
        except Exception as e:
            db.session.rollback()
            flash(f'Error al actualizar el usuario: {str(e)}', 'danger')
    
    return render_template('users/edit.html', usuario=usuario)

@users_bp.route('/toggle_status/<int:user_id>')
@login_required
def toggle_status(user_id):
    """
    Activar/desactivar un usuario
    """
    # Verificar si el usuario actual es administrador
    if current_user.rol != 'admin':
        flash('No tiene permisos para acceder a esta sección', 'danger')
        return redirect(url_for('dashboard.index'))
    
    # Obtener el usuario
    usuario = Usuario.query.get_or_404(user_id)
    
    try:
        # No permitir desactivar el último administrador
        if usuario.rol == 'admin' and usuario.activo:
            admin_count = Usuario.query.filter_by(rol='admin', activo=True).count()
            if admin_count <= 1:
                flash('No se puede desactivar el último administrador activo', 'danger')
                return redirect(url_for('users.index'))
        
        # Cambiar el estado
        usuario.activo = not usuario.activo
        db.session.commit()
        
        estado = "activado" if usuario.activo else "desactivado"
        flash(f'Usuario {usuario.nombre} {estado} correctamente', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error al cambiar el estado del usuario: {str(e)}', 'danger')
    
    return redirect(url_for('users.index'))

@users_bp.route('/change_password/<int:user_id>', methods=['GET', 'POST'])
@login_required
def change_password(user_id):
    """
    Cambiar la contraseña de un usuario
    """
    # Verificar si el usuario actual es administrador
    if current_user.rol != 'admin':
        flash('No tiene permisos para acceder a esta sección', 'danger')
        return redirect(url_for('dashboard.index'))
    
    # Obtener el usuario
    usuario = Usuario.query.get_or_404(user_id)
    
    if request.method == 'POST':
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        
        # Validar datos
        if not password:
            flash('La contraseña es obligatoria', 'danger')
            return render_template('users/change_password.html', usuario=usuario)
        
        if password != confirm_password:
            flash('Las contraseñas no coinciden', 'danger')
            return render_template('users/change_password.html', usuario=usuario)
        
        try:
            # Actualizar la contraseña
            usuario.password_hash = generate_password_hash(password)
            db.session.commit()

            flash('Contraseña actualizada correctamente', 'success')
            return redirect(url_for('users.index'))
        except Exception as e:
            db.session.rollback()
            flash(f'Error al actualizar la contraseña: {str(e)}', 'danger')

    return render_template('users/change_password.html', usuario=usuario)

@users_bp.route('/delete/<int:user_id>', methods=['POST'])
@login_required
def delete_user(user_id):
    """
    Eliminar un usuario
    """
    # Verificar si el usuario actual es administrador
    if current_user.rol != 'admin':
        flash('No tiene permisos para acceder a esta sección', 'danger')
        return redirect(url_for('dashboard.index'))

    # Obtener el usuario
    usuario = Usuario.query.get_or_404(user_id)

    # No permitir eliminar el último administrador
    if usuario.rol == 'admin':
        admin_count = Usuario.query.filter_by(rol='admin').count()
        if admin_count <= 1:
            flash('No se puede eliminar el último administrador', 'danger')
            return redirect(url_for('users.index'))

    # No permitir que un usuario se elimine a sí mismo
    if usuario.id == current_user.id:
        flash('No se puede eliminar su propio usuario', 'danger')
        return redirect(url_for('users.index'))

    try:
        # Eliminar el usuario
        db.session.delete(usuario)
        db.session.commit()

        flash(f'Usuario {usuario.nombre} eliminado correctamente', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error al eliminar el usuario: {str(e)}', 'danger')

    return redirect(url_for('users.index'))
