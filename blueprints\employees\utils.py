# -*- coding: utf-8 -*-
from datetime import datetime
from dateutil.relativedelta import relativedelta

def format_remaining_time(today, target_date):
    """Convierte la diferencia de tiempo en un formato legible (meses, días)."""
    if target_date < today:
        return "Vencido"

    delta = relativedelta(target_date, today)
    
    parts = []
    if delta.months > 0:
        parts.append(f"{delta.months} mes{'es' if delta.months > 1 else ''}")
    
    if delta.days > 0 or (delta.months == 0 and delta.days == 0):
        if delta.days == 0 and delta.months > 0:
             pass # No mostrar 'y 0 días'
        else:
             parts.append(f"{delta.days} día{'s' if delta.days != 1 else ''}")
        
    if not parts:
        return "Hoy"

    return " y ".join(parts)

def get_contract_status(fecha_finalizacion, fecha_ingreso):
    """
    Calcula los días restantes y el estado de un contrato.
    Si no hay fecha de finalización, la proyecta basándose en la fecha de ingreso y ciclos de 6/12 meses.
    Devuelve: dias_restantes, estado, fecha_formateada, tiempo_formateado, tipo_renovacion (6, 12, o None)
    """
    today = datetime.now().date()
    target_date = None
    renewal_type = None

    if fecha_finalizacion:
        if isinstance(fecha_finalizacion, str):
            try:
                target_date = datetime.strptime(fecha_finalizacion, '%Y-%m-%d').date()
            except (ValueError, TypeError):
                pass
        else:
            target_date = fecha_finalizacion

    elif fecha_ingreso:
        if isinstance(fecha_ingreso, str):
            try:
                fecha_ingreso = datetime.strptime(fecha_ingreso, '%Y-%m-%d').date()
            except (ValueError, TypeError):
                return None, None, None, None, None

        next_renewal_6 = fecha_ingreso
        while next_renewal_6 <= today:
            next_renewal_6 += relativedelta(months=6)

        next_renewal_12 = fecha_ingreso
        while next_renewal_12 <= today:
            next_renewal_12 += relativedelta(months=12)

        if next_renewal_6 < next_renewal_12:
            target_date = next_renewal_6
            renewal_type = 6
        else:
            target_date = next_renewal_12
            renewal_type = 12

    if not target_date:
        return None, None, None, None, None

    delta = target_date - today
    days_remaining = delta.days

    if days_remaining < 0:
        status = 'vencido'
    elif days_remaining <= 30:
        status = 'critico'
    elif days_remaining <= 90:
        status = 'alerta'
    else:
        status = 'ok'

    formatted_date = target_date.strftime('%d/%m/%Y')
    remaining_time_formatted = format_remaining_time(today, target_date)

    return days_remaining, status, formatted_date, remaining_time_formatted, renewal_type
