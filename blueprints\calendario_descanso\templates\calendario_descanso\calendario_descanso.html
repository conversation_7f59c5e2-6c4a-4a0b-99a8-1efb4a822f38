{% extends 'base.html' %}
{% block content %}
<div class="container mt-4">
    <h2>Calendario de Descanso Semanal</h2>

    <!-- Filtros principales -->
    <div class="row g-3 mb-4 p-3 border rounded bg-light">
        <div class="col-md-3">
            <label for="select-turno" class="form-label"><strong>Turno</strong> <span
                    class="text-danger">*</span></label>
            <select class="form-select" id="select-turno" required>
                <option value="">Cargando turnos...</option>
            </select>
        </div>
        <div class="col-md-3">
            <label for="input-fecha" class="form-label"><strong>Fecha de la Semana</strong> <span
                    class="text-danger">*</span></label>
            <input type="date" class="form-control" id="input-fecha" required>
        </div>
        <div class="col-md-2">
            <label for="select-sector" class="form-label"><strong>Sector</strong></label>
            <select class="form-select" id="select-sector">
                <option value="">Todos los sectores</option>
            </select>
        </div>
        <div class="col-md-2">
            <label for="select-departamento" class="form-label"><strong>Departamento</strong></label>
            <select class="form-select" id="select-departamento">
                <option value="">Todos los departamentos</option>
            </select>
        </div>
        <div class="col-md-2 d-flex align-items-end">
            <button class="btn btn-secondary w-100" id="btn-ver-calendario">
                <i class="bi bi-calendar-week"></i> Ver Semana
            </button>
        </div>
    </div>

    <!-- Panel de Asignación -->
    <div id="panel-asignacion" class="d-none">
        <div class="row g-3 mb-4 p-3 border rounded bg-light">
            <div class="col-md-9">
                <p class="mb-1"><strong>Turno:</strong> <span id="turno-seleccionado-info"></span></p>
                <p class="mb-0"><strong>Semana del:</strong> <span id="semana-info"></span></p>
            </div>
            <div class="col-md-3 d-flex align-items-center">
                <button class="btn btn-success w-100" id="btn-asignar-masivo">
                    <i class="bi bi-people-fill"></i> Asignar Descanso
                </button>
            </div>
        </div>

        <!-- Tabla de Empleados -->
        <div class="table-responsive">
            <table class="table table-bordered table-hover table-sm align-middle" id="tabla-empleados">
                <thead class="table-light">
                    <tr>
                        <th rowspan="2" class="text-center align-middle">Empleado</th>
                        <th rowspan="2" class="text-center align-middle">Sector</th>
                        <th rowspan="2" class="text-center align-middle">Departamento</th>
                        <th colspan="7" class="text-center" id="th-semana">Días de la Semana</th>
                        <th rowspan="2" class="text-center align-middle">Observaciones</th>
                    </tr>
                    <tr id="tr-dias-semana">
                        <!-- Cabeceras de días se insertan aquí -->
                    </tr>
                </thead>
                <tbody id="tbody-empleados">
                    <!-- Filas de empleados se insertan aquí -->
                </tbody>
            </table>
        </div>

        <!-- Acciones -->
        <div class="d-flex justify-content-end mt-3">
            <button class="btn btn-primary" id="btn-guardar">
                <i class="bi bi-save"></i> Guardar Cambios
            </button>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const selectTurno = document.getElementById('select-turno');
        const selectSector = document.getElementById('select-sector');
        const selectDepartamento = document.getElementById('select-departamento');
        const inputFecha = document.getElementById('input-fecha');
        const btnVerCalendario = document.getElementById('btn-ver-calendario');
        const panelAsignacion = document.getElementById('panel-asignacion');
        const tbodyEmpleados = document.getElementById('tbody-empleados');
        const thSemana = document.getElementById('th-semana');
        const trDiasSemana = document.getElementById('tr-dias-semana');
        const turnoSeleccionadoInfo = document.getElementById('turno-seleccionado-info');
        const semanaInfo = document.getElementById('semana-info');
        const btnGuardar = document.getElementById('btn-guardar');
        const btnAsignarMasivo = document.getElementById('btn-asignar-masivo');

        const tiposEstado = {{ tipos_estado | tojson | safe
    }};
    let empleados = [];
    let descansos = {}; // { "empleadoId-fecha": "TIPO_ESTADO" }
    let semanaActual = [];
    let sectores = [];
    let departamentos = [];

    // --- CARGA INICIAL ---
    function cargarTurnos() {
        console.log('Intentando cargar turnos...');
        fetch('/calendario_descanso/api/turnos')
            .then(response => {
                console.log('Respuesta recibida:', response.status, response.statusText);
                if (!response.ok) {
                    return response.text().then(text => {
                        console.error('Error en la respuesta:', text);
                        throw new Error(`Error al cargar turnos: ${response.status} ${response.statusText}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                console.log('Datos recibidos:', data);
                if (!Array.isArray(data)) {
                    throw new Error('La respuesta no es un array');
                }
                selectTurno.innerHTML = '<option value="">Seleccione un turno</option>' +
                    data.map(t => `<option value="${t.id}">${t.tipo}</option>`).join('');
                console.log('Turnos cargados correctamente');
            })
            .catch(error => {
                console.error('Error en cargarTurnos:', error);
                selectTurno.innerHTML = '<option value="">Error al cargar. Ver consola.</option>';
            });
    }

    function cargarSectores() {
        console.log('Cargando sectores...');
        fetch('/calendario_descanso/api/sectores')
            .then(response => response.json())
            .then(data => {
                sectores = data;
                selectSector.innerHTML = '<option value="">Todos los sectores</option>' +
                    data.map(s => `<option value="${s.id}">${s.nombre}</option>`).join('');
                console.log('Sectores cargados correctamente');
            })
            .catch(error => {
                console.error('Error al cargar sectores:', error);
                selectSector.innerHTML = '<option value="">Error al cargar sectores</option>';
            });
    }

    function cargarDepartamentos() {
        console.log('Cargando departamentos...');
        fetch('/calendario_descanso/api/departamentos')
            .then(response => response.json())
            .then(data => {
                departamentos = data;
                selectDepartamento.innerHTML = '<option value="">Todos los departamentos</option>' +
                    data.map(d => `<option value="${d.id}">${d.nombre}</option>`).join('');
                console.log('Departamentos cargados correctamente');
            })
            .catch(error => {
                console.error('Error al cargar departamentos:', error);
                selectDepartamento.innerHTML = '<option value="">Error al cargar departamentos</option>';
            });
    }

    // --- LÓGICA DE DATOS ---
    function fetchEmpleadosYDescansos() {
        const turnoId = selectTurno.value;
        const sectorId = selectSector.value;
        const departamentoId = selectDepartamento.value;

        if (!turnoId || semanaActual.length === 0) return;

        const fechaInicio = semanaActual[0];
        const fechaFin = semanaActual[6];

        // Construir URL con parámetros de filtro
        let empleadosUrl = `/calendario_descanso/api/empleados?turno_id=${turnoId}`;
        if (sectorId) empleadosUrl += `&sector_id=${sectorId}`;
        if (departamentoId) empleadosUrl += `&departamento_id=${departamentoId}`;

        const empleadosPromise = fetch(empleadosUrl).then(r => r.json());
        const descansosPromise = fetch(`/calendario_descanso/api/descansos?turno_id=${turnoId}&fecha_inicio=${fechaInicio}&fecha_fin=${fechaFin}`).then(r => r.json());

        Promise.all([empleadosPromise, descansosPromise])
            .then(([empleadosData, descansosData]) => {
                empleados = empleadosData;
                descansos = {};
                if (descansosData) {
                    descansosData.forEach(d => {
                        descansos[`${d.empleado_id}-${d.fecha}`] = d.tipo;
                    });
                }
                renderTabla();
            })
            .catch(error => {
                console.error('Error fetching data:', error);
                tbodyEmpleados.innerHTML = `<tr><td colspan="11" class="text-center text-danger">Error al cargar los datos.</td></tr>`;
            });
    }

    // --- RENDERIZADO ---
    function renderTabla() {
        tbodyEmpleados.innerHTML = '';
        if (empleados.length === 0) {
            tbodyEmpleados.innerHTML = `<tr><td colspan="11" class="text-center">No hay empleados para los filtros seleccionados.</td></tr>`;
            return;
        }

        empleados.forEach(emp => {
            const row = document.createElement('tr');
            row.dataset.empleadoId = emp.id;

            let celdasDias = semanaActual.map(fecha => {
                const key = `${emp.id}-${fecha}`;
                const tipoActual = descansos[key] || 'TRABAJA';

                const options = tiposEstado.map(tipo =>
                    `<option value="${tipo}" ${tipo === tipoActual ? 'selected' : ''}>${tipo}</option>`
                ).join('');

                return `<td class="text-center p-1"><select class="form-select form-select-sm" data-fecha="${fecha}">${options}</select></td>`;
            }).join('');

            row.innerHTML = `
                <td>${emp.apellidos}, ${emp.nombre}</td>
                <td>${emp.sector_nombre || 'N/A'}</td>
                <td>${emp.departamento_nombre || 'N/A'}</td>
                ${celdasDias}
                <td class="p-1"><input type="text" class="form-control form-control-sm" placeholder="Observación..."></td>
            `;
            tbodyEmpleados.appendChild(row);
        });
    }

    function renderEncabezadoSemana() {
        trDiasSemana.innerHTML = '';
        semanaActual.forEach(fecha => {
            const dia = new Date(fecha + 'T00:00:00').toLocaleDateString('es-ES', { weekday: 'short', day: 'numeric' });
            trDiasSemana.innerHTML += `<th class="text-center">${dia}</th>`;
        });
    }

    // --- MANEJADORES DE EVENTOS ---
    btnVerCalendario.addEventListener('click', () => {
        const turnoId = selectTurno.value;
        const fechaSeleccionada = inputFecha.value;

        if (!turnoId) {
            alert('Por favor, seleccione un turno.');
            return;
        }
        if (!fechaSeleccionada) {
            alert('Por favor, seleccione una fecha.');
            return;
        }

        const fecha = new Date(fechaSeleccionada + 'T00:00:00');
        const diaSemana = fecha.getDay();
        const primerDia = new Date(fecha);
        primerDia.setDate(fecha.getDate() - (diaSemana === 0 ? 6 : diaSemana - 1));

        semanaActual = [];
        for (let i = 0; i < 7; i++) {
            const d = new Date(primerDia);
            d.setDate(primerDia.getDate() + i);
            semanaActual.push(d.toISOString().split('T')[0]);
        }

        turnoSeleccionadoInfo.textContent = selectTurno.options[selectTurno.selectedIndex].text;
        semanaInfo.textContent = `${semanaActual[0]} al ${semanaActual[6]}`;
        panelAsignacion.classList.remove('d-none');

        renderEncabezadoSemana();
        fetchEmpleadosYDescansos();
    });

    btnAsignarMasivo.addEventListener('click', () => {
        const turnoId = selectTurno.value;
        if (!turnoId) return;

        const confirmacion = confirm(`¿Desea asignar DESCANSO a TODOS los empleados visibles del turno para toda la semana seleccionada?`);
        if (!confirmacion) return;

        tbodyEmpleados.querySelectorAll('tr[data-empleado-id]').forEach(row => {
            row.querySelectorAll('select[data-fecha]').forEach(select => {
                select.value = 'DESCANSO';
            });
        });
        alert('Se han marcado todos los días como DESCANSO. Pulse "Guardar Cambios" para confirmar.');
    });

    btnGuardar.addEventListener('click', () => {
        const datosAGuardar = [];
        tbodyEmpleados.querySelectorAll('tr[data-empleado-id]').forEach(row => {
            const empleadoId = row.dataset.empleadoId;
            row.querySelectorAll('select[data-fecha]').forEach(select => {
                datosAGuardar.push({
                    empleado_id: parseInt(empleadoId),
                    fecha: select.dataset.fecha,
                    tipo: select.value,
                    observacion: row.querySelector('input[type="text"]').value
                });
            });
        });

        fetch('/calendario_descanso/api/guardar_descansos', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ descansos: datosAGuardar })
        })
            .then(response => response.json())
            .then(data => {
                if (data.ok) {
                    alert('Cambios guardados con éxito.');
                    fetchEmpleadosYDescansos();
                } else {
                    alert('Error al guardar los cambios: ' + (data.error || 'Error desconocido'));
                }
            })
            .catch(error => {
                console.error('Error al guardar:', error);
                alert('Error de conexión al guardar los cambios.');
            });
    });

    // --- INICIALIZACIÓN ---
    cargarTurnos();
    cargarSectores();
    cargarDepartamentos();
});
</script>
{% endblock %}