<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "urn:fontconfig:fonts.dtd">
<fontconfig>
  <description>Set substitutions for similar/metric-compatible families</description>

<!--

Alias similar/metric-compatible families from various sources:

PostScript fonts:       URW fonts:           GUST fonts:        Windows fonts:
======================  ==================  =================  ==================
Helvetica               Nimbus Sans         TeX Gyre Heros
Helvetica Narrow        Nimbus Sans Narrow  TeX Gyre Heros Cn
Times                   Nimbus Roman        TeX Gyre Termes
Courier                 Nimbus Mono PS      TeX Gyre Cursor
ITC Avant Garde Gothic  URW Gothic          TeX Gyre Adventor
ITC Bookman             URW Bookman         TeX Gyre Bonum     Bookman Old Style
ITC Zapf Chancery       Z003                TeX Gyre Chorus
Palatino                P052                TeX Gyre Pagella   Palatino Linotype
New Century Schoolbook  C059                TeX Gyre Schola    Century Schoolbook

Microsoft fonts:  Liberation fonts:       Google CrOS core fonts:  StarOffice fonts:  AMT fonts:
================  ======================  =======================  =================  ==============
Arial             Liberation Sans         Arimo                    Albany             Albany AMT
Arial Narrow      Liberation Sans Narrow
Times New Roman   Liberation Serif        Tinos                    Thorndale          Thorndale AMT
Courier New       Liberation Mono         Cousine                  Cumberland         Cumberland AMT
Cambria                                   Caladea
Calibri                                   Carlito
Symbol                                    SymbolNeu

Microsoft fonts:  Other fonts:
================  ============
Georgia           Gelasio

We want for each of them to fallback to any of these available,
but in an order preferring similar designs first.  We do this in three steps:

1) Alias each specific to its generic family.
   e.g. Liberation Sans to Arial

2) Weak alias each generic to the other generic of its family.
   e.g. Arial to Helvetica

3) Alias each generic to its specifics.
   e.g. Arial to Liberation Sans, Arimo, Albany, and Albany AMT

NOTE: The (URW)++ fonts mappings of generics to specifics were removed, because
      upstream now includes them in their release of (URW)++ Core Font Set here:
      https://github.com/ArtifexSoftware/urw-base35-fonts/tree/master/fontconfig
-->

<!-- Map specifics to generics -->

	<!-- PostScript -->
	<alias binding="same">
	  <family>Nimbus Sans L</family>
	  <default>
	  <family>Helvetica</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Nimbus Sans</family>
	  <default>
	  <family>Helvetica</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>TeX Gyre Heros</family>
	  <default>
	  <family>Helvetica</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Nimbus Sans Narrow</family>
	  <default>
	  <family>Helvetica Narrow</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>TeX Gyre Heros Cn</family>
	  <default>
	  <family>Helvetica Narrow</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Nimbus Roman No9 L</family>
	  <default>
	  <family>Times</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Nimbus Roman</family>
	  <default>
	  <family>Times</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>TeX Gyre Termes</family>
	  <default>
	  <family>Times</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Nimbus Mono L</family>
	  <default>
	  <family>Courier</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Nimbus Mono</family>
	  <default>
	  <family>Courier</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Nimbus Mono PS</family>
	  <default>
	  <family>Courier</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>TeX Gyre Cursor</family>
	  <default>
	  <family>Courier</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Avant Garde</family>
	  <default>
	  <family>ITC Avant Garde Gothic</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>URW Gothic L</family>
	  <default>
	  <family>ITC Avant Garde Gothic</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>URW Gothic</family>
	  <default>
	  <family>ITC Avant Garde Gothic</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>TeX Gyre Adventor</family>
	  <default>
	  <family>ITC Avant Garde Gothic</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Bookman</family>
	  <default>
	  <family>ITC Bookman</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>URW Bookman L</family>
	  <default>
	  <family>ITC Bookman</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Bookman URW</family>
	  <default>
	  <family>ITC Bookman</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>URW Bookman</family>
	  <default>
	  <family>ITC Bookman</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>TeX Gyre Bonum</family>
	  <default>
	  <family>ITC Bookman</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Bookman Old Style</family>
	  <default>
	  <family>ITC Bookman</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Zapf Chancery</family>
	  <default>
	  <family>ITC Zapf Chancery</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>URW Chancery L</family>
	  <default>
	  <family>ITC Zapf Chancery</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Chancery URW</family>
	  <default>
	  <family>ITC Zapf Chancery</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Z003</family>
	  <default>
	  <family>ITC Zapf Chancery</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>TeX Gyre Chorus</family>
	  <default>
	  <family>ITC Zapf Chancery</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>URW Palladio L</family>
	  <default>
	  <family>Palatino</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Palladio URW</family>
	  <default>
	  <family>Palatino</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>P052</family>
	  <default>
	  <family>Palatino</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>TeX Gyre Pagella</family>
	  <default>
	  <family>Palatino</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Palatino Linotype</family>
	  <default>
	  <family>Palatino</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Century Schoolbook L</family>
	  <default>
	  <family>New Century Schoolbook</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Century SchoolBook URW</family>
	  <default>
	  <family>New Century Schoolbook</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>C059</family>
	  <default>
	  <family>New Century Schoolbook</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>TeX Gyre Schola</family>
	  <default>
	  <family>New Century Schoolbook</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Century Schoolbook</family>
	  <default>
	  <family>New Century Schoolbook</family>
	  </default>
	</alias>

	<!-- Microsoft -->
	<alias binding="same">
	  <family>Arimo</family>
	  <default>
	    <family>Arial</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Liberation Sans</family>
	  <default>
	  <family>Arial</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Liberation Sans Narrow</family>
	  <default>
	    <family>Arial Narrow</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Albany</family>
	  <default>
	  <family>Arial</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Albany AMT</family>
	  <default>
	  <family>Arial</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Tinos</family>
	  <default>
	    <family>Times New Roman</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Liberation Serif</family>
	  <default>
	  <family>Times New Roman</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Thorndale</family>
	  <default>
	  <family>Times New Roman</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Thorndale AMT</family>
	  <default>
	  <family>Times New Roman</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Cousine</family>
	  <default>
	    <family>Courier New</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Liberation Mono</family>
	  <default>
	  <family>Courier New</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Cumberland</family>
	  <default>
	  <family>Courier New</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Cumberland AMT</family>
	  <default>
	  <family>Courier New</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Gelasio</family>
	  <default>
	  <family>Georgia</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Caladea</family>
	  <default>
	  <family>Cambria</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>Carlito</family>
	  <default>
	  <family>Calibri</family>
	  </default>
	</alias>

	<alias binding="same">
	  <family>SymbolNeu</family>
	  <default>
	  <family>Symbol</family>
	  </default>
  </alias>

<!-- Accept the other group as fallback -->

	<!-- PostScript -->
	<alias>
	  <family>Helvetica</family>
	  <default>
	  <family>Arial</family>
	  </default>
	</alias>

	<alias>
	  <family>Helvetica Narrow</family>
	  <default>
	  <family>Arial Narrow</family>
	  </default>
	</alias>

	<alias>
	  <family>Times</family>
	  <default>
	  <family>Times New Roman</family>
	  </default>
	</alias>

	<alias>
	  <family>Courier</family>
	  <default>
	  <family>Courier New</family>
	  </default>
	</alias>

	<!-- Microsoft -->
	<alias>
	  <family>Arial</family>
	  <default>
	  <family>Helvetica</family>
	  </default>
	</alias>

	<alias>
	  <family>Arial Narrow</family>
	  <default>
	  <family>Helvetica Narrow</family>
	  </default>
	</alias>

	<alias>
	  <family>Times New Roman</family>
	  <default>
	  <family>Times</family>
	  </default>
	</alias>

	<alias>
	  <family>Courier New</family>
	  <default>
	  <family>Courier</family>
	  </default>
	</alias>

<!-- Map generics to specifics -->

	<!-- PostScript -->
	<alias binding="same">
	  <family>Helvetica</family>
	  <accept>
	  <family>TeX Gyre Heros</family>
	  </accept>
	</alias>

	<alias binding="same">
	  <family>Helvetica Narrow</family>
	  <accept>
	  <family>TeX Gyre Heros Cn</family>
	  </accept>
	</alias>

	<alias binding="same">
	  <family>Times</family>
	  <accept>
	  <family>TeX Gyre Termes</family>
	  </accept>
	</alias>

	<alias binding="same">
	  <family>Courier</family>
	  <accept>
	  <family>TeX Gyre Cursor</family>
	  </accept>
	</alias>

	<alias binding="same">
	  <family>Courier Std</family>
	  <accept>
	  <family>Courier</family>
	  </accept>
	</alias>

	<alias binding="same">
	  <family>ITC Avant Garde Gothic</family>
	  <accept>
	  <family>TeX Gyre Adventor</family>
	  </accept>
	</alias>

	<alias binding="same">
	  <family>ITC Bookman</family>
	  <accept>
	  <family>Bookman Old Style</family>
	  <family>TeX Gyre Bonum</family>
	  </accept>
	</alias>

	<alias binding="same">
	  <family>ITC Zapf Chancery</family>
	  <accept>
	  <family>TeX Gyre Chorus</family>
	  </accept>
	</alias>

	<alias binding="same">
	  <family>Palatino</family>
	  <accept>
	  <family>Palatino Linotype</family>
	  <family>TeX Gyre Pagella</family>
	  </accept>
	</alias>

	<alias binding="same">
	  <family>New Century Schoolbook</family>
	  <accept>
	  <family>Century Schoolbook</family>
	  <family>TeX Gyre Schola</family>
	  </accept>
	</alias>

	<!-- Microsoft -->
	<alias binding="same">
	  <family>Arial</family>
	  <accept>
	    <family>Arimo</family>
	    <family>Liberation Sans</family>
	    <family>Albany</family>
	    <family>Albany AMT</family>
	  </accept>
	</alias>

	<alias binding="same">
	  <family>Arial Narrow</family>
	  <accept>
	    <family>Liberation Sans Narrow</family>
	  </accept>
	</alias>

	<alias binding="same">
	  <family>Times New Roman</family>
	  <accept>
	    <family>Tinos</family>
	    <family>Liberation Serif</family>
	    <family>Thorndale</family>
	    <family>Thorndale AMT</family>
	  </accept>
	</alias>

	<alias binding="same">
	  <family>Courier New</family>
	  <accept>
	    <family>Cousine</family>
	    <family>Liberation Mono</family>
	    <family>Cumberland</family>
	    <family>Cumberland AMT</family>
	  </accept>
	</alias>

	<alias binding="same">
	  <family>Georgia</family>
	  <accept>
	    <family>Gelasio</family>
	  </accept>
	</alias>

	<alias binding="same">
	  <family>Cambria</family>
	  <accept>
	    <family>Caladea</family>
	  </accept>
	</alias>

	<alias binding="same">
	  <family>Calibri</family>
	  <accept>
	    <family>Carlito</family>
	  </accept>
  </alias>

	<alias binding="same">
	  <family>Symbol</family>
	  <accept>
	    <family>SymbolNeu</family>
	  </accept>
  </alias>

</fontconfig>
