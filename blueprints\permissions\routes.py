# -*- coding: utf-8 -*-
from flask import render_template, request, redirect, url_for, flash, send_file
from . import permissions_bp
from models import Permiso, Empleado, db, TIPOS_PERMISO, HistorialCambios
from services.permission_service import PermissionService
from services.duration_service import DurationService, duration_service
from datetime import datetime
import io
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import traceback
import logging
from flask_login import current_user
from services.audit_service import AuditService

permission_service = PermissionService()

@permissions_bp.route('/solicitar', methods=['GET', 'POST'])
def solicitar_permiso():
    """
    Maneja la solicitud de un nuevo permiso
    """
    if request.method == 'POST':
        # Procesar la solicitud usando el servicio
        permiso, error = permission_service.request_permission(request.form)

        if error:
            flash(f"Error al solicitar permiso: {error}", "error")
            return redirect(url_for('permissions.solicitar_permiso'))
        else:
            flash("Permiso solicitado correctamente", "success")
            return redirect(url_for('permissions.list_permissions'))

    # Para solicitudes GET, mostrar el formulario
    empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.ficha).all()
    return render_template('solicitar_permiso.html',
                         empleados=empleados,
                         tipos_permiso=TIPOS_PERMISO)

@permissions_bp.route('/')
def list_permissions():
    # Obtener parámetros de filtro
    page_actuales = request.args.get('page_actuales', 1, type=int)
    page_pasados = request.args.get('page_pasados', 1, type=int)
    per_page = 5  # Reducimos a 5 para mostrar más compacto cada sección
    estado = request.args.get('estado', '')
    tipo_permiso = request.args.get('tipo_permiso', '')
    empleado_id = request.args.get('empleado_id', '')
    fecha_desde = request.args.get('fecha_desde', '')
    fecha_hasta = request.args.get('fecha_hasta', '')
    busqueda = request.args.get('busqueda', '')

    # Obtener permisos filtrados y separados por estado (actuales/futuros y pasados)
    permisos_actuales_futuros, permisos_pasados = permission_service.get_filtered_permissions(
        estado=estado,
        tipo_permiso=tipo_permiso,
        empleado_id=empleado_id,
        fecha_desde=fecha_desde,
        fecha_hasta=fecha_hasta,
        busqueda=busqueda,
        separar_por_estado=True
    )

    # Obtener lista de empleados para el filtro
    empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.ficha).all()

    # Calcular totales
    total_actuales_futuros = len(permisos_actuales_futuros)
    total_pasados = len(permisos_pasados)
    total_permisos = total_actuales_futuros + total_pasados

    # Calcular total de páginas para cada sección
    total_paginas_actuales = (total_actuales_futuros + per_page - 1) // per_page if total_actuales_futuros > 0 else 1
    total_paginas_pasados = (total_pasados + per_page - 1) // per_page if total_pasados > 0 else 1

    # Aplicar paginación independiente para cada sección
    # Permisos actuales/futuros
    start_actuales = (page_actuales - 1) * per_page
    end_actuales = start_actuales + per_page
    actuales_futuros_a_mostrar = permisos_actuales_futuros[start_actuales:min(end_actuales, total_actuales_futuros)] if total_actuales_futuros > 0 else []

    # Permisos pasados
    start_pasados = (page_pasados - 1) * per_page
    end_pasados = start_pasados + per_page
    pasados_a_mostrar = permisos_pasados[start_pasados:min(end_pasados, total_pasados)] if total_pasados > 0 else []

    return render_template('permissions/list.html',
                         permisos_actuales_futuros=actuales_futuros_a_mostrar,
                         permisos_pasados=pasados_a_mostrar,
                         empleados=empleados,
                         page_actuales=page_actuales,
                         page_pasados=page_pasados,
                         per_page=per_page,
                         total_paginas_actuales=total_paginas_actuales,
                         total_paginas_pasados=total_paginas_pasados,
                         total_permisos=total_permisos,
                         total_actuales_futuros=total_actuales_futuros,
                         total_pasados=total_pasados)

@permissions_bp.route('/gestionar')
def manage_permissions():
    """
    Gestión de permisos (aprobar/rechazar)
    """
    # Obtener parámetros de filtro
    estado = request.args.get('estado', 'Pendiente')
    tipo_permiso = request.args.get('tipo_permiso', '')
    empleado_id = request.args.get('empleado_id', '')
    fecha_desde = request.args.get('fecha_desde', '')
    fecha_hasta = request.args.get('fecha_hasta', '')
    busqueda = request.args.get('busqueda', '')
    solo_indefinidas = request.args.get('solo_indefinidas', '')
    duracion_minima = request.args.get('duracion_minima', '')
    ordenar_por = request.args.get('ordenar_por', 'fecha_inicio_desc')
    pagina = request.args.get('pagina', 1, type=int)

    # Preparar la consulta base
    query = Permiso.query

    # Aplicar filtros
    if estado:
        query = query.filter(Permiso.estado == estado)
    if tipo_permiso:
        query = query.filter(Permiso.tipo_permiso == tipo_permiso)
    if empleado_id:
        query = query.filter(Permiso.empleado_id == empleado_id)
    if fecha_desde:
        query = query.filter(Permiso.fecha_inicio >= datetime.strptime(fecha_desde, '%Y-%m-%d'))
    if fecha_hasta:
        query = query.filter(Permiso.fecha_fin <= datetime.strptime(fecha_hasta, '%Y-%m-%d'))
    if busqueda:
        # Buscar en nombre, apellidos o motivo
        # Especificar explícitamente la relación para evitar ambigüedad
        query = query.join(Empleado, Permiso.empleado_id == Empleado.id).filter(
            (Empleado.nombre.ilike(f'%{busqueda}%')) |
            (Empleado.apellidos.ilike(f'%{busqueda}%')) |
            (Permiso.motivo.ilike(f'%{busqueda}%'))
        )
    if solo_indefinidas == '1':
        # Filtrar solo bajas médicas indefinidas
        query = query.filter(
            Permiso.tipo_permiso == 'Baja Médica',
            Permiso.sin_fecha_fin == True
        )

    # Filtrar por duración mínima si se especifica
    # Este filtro se aplicará después de obtener los resultados, ya que requiere cálculos
    aplicar_filtro_duracion = duracion_minima and duracion_minima.isdigit() and int(duracion_minima) > 0

    # Aplicar ordenación según el parámetro
    if ordenar_por == 'fecha_inicio_asc':
        query = query.order_by(Permiso.fecha_inicio.asc())
    elif ordenar_por == 'duracion_desc' or ordenar_por == 'duracion_asc':
        # Para ordenar por duración, primero obtenemos todos los resultados
        # y luego ordenamos manualmente, ya que la duración puede ser calculada
        pass  # Se aplicará después de obtener los resultados
    elif ordenar_por == 'fecha_actual_centro':
        # Para ordenar con la fecha actual como centro, primero obtenemos todos los resultados
        # y luego ordenamos manualmente usando la función especializada
        pass  # Se aplicará después de obtener los resultados
    else:  # Por defecto: fecha_inicio_desc
        query = query.order_by(Permiso.fecha_inicio.desc())

    # Obtener todos los permisos para contar el total
    permisos_totales = query.all()

    # Aplicar filtro por duración mínima si es necesario
    if aplicar_filtro_duracion:
        duracion_min = int(duracion_minima)
        # Usar el servicio de duración para filtrar permisos
        permisos_totales = duration_service.obtener_permisos_por_duracion(permisos_totales, duracion_min)
    else:
        # Calcular duración para todos los permisos (para ordenación)
        duraciones = duration_service.calcular_duraciones_multiples(permisos_totales)
        for permiso in permisos_totales:
            if permiso.id in duraciones:
                permiso.duracion_calculada = duraciones[permiso.id]

    # Aplicar ordenación por duración o fecha actual como centro si es necesario
    if ordenar_por == 'duracion_desc' or ordenar_por == 'duracion_asc':
        # Usar el servicio de duración para ordenar permisos
        ascendente = (ordenar_por == 'duracion_asc')
        permisos_totales = duration_service.ordenar_permisos_por_duracion(permisos_totales, ascendente)
    elif ordenar_por == 'fecha_actual_centro':
        # Usar el servicio de duración para ordenar permisos con la fecha actual como centro
        permisos_totales = duration_service.ordenar_permisos_por_fecha_actual(permisos_totales)

    total_permisos = len(permisos_totales)

    # Paginación manual
    per_page = 10
    total_paginas = (total_permisos + per_page - 1) // per_page if total_permisos > 0 else 1

    # Ajustar la página actual si está fuera de rango
    if pagina < 1:
        pagina = 1
    if pagina > total_paginas:
        pagina = total_paginas

    # Aplicar paginación manual
    start = (pagina - 1) * per_page
    end = start + per_page
    permisos_paginados = permisos_totales[start:end] if total_permisos > 0 else []

    # Obtener lista de empleados para el filtro
    empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.ficha).all()

    # Obtener contadores para todos los estados
    contadores = {
        'pendientes': Permiso.query.filter_by(estado='Pendiente').count(),
        'aprobados': Permiso.query.filter_by(estado='Aprobado').count(),
        'denegados': Permiso.query.filter_by(estado='Denegado').count()
    }

    return render_template('gestion_permisos.html',
                         permisos=permisos_paginados,
                         empleados=empleados,
                         contadores=contadores,
                         estado_actual=estado,
                         tipo_permiso_actual=tipo_permiso,
                         empleado_id_actual=empleado_id,
                         fecha_desde=fecha_desde,
                         fecha_hasta=fecha_hasta,
                         busqueda=busqueda,
                         pagina_actual=pagina,
                         total_paginas=total_paginas,
                         total_permisos=total_permisos,
                         solo_indefinidas=solo_indefinidas,
                         duracion_minima=duracion_minima,
                         ordenar_por=ordenar_por,
                         tipos_permiso=TIPOS_PERMISO)

@permissions_bp.route('/aprobar/<int:id>', methods=['POST'])
def aprobar_permiso(id):
    """Aprobar un permiso"""
    try:
        permiso = Permiso.query.get_or_404(id)
        estado_anterior = permiso.estado

        # Actualizar el permiso
        permiso.estado = 'Aprobado'
        permiso.observaciones_revision = request.form.get('observaciones', '')
        permiso.fecha_revision = datetime.now()
        # Usar current_user si está disponible
        try:
            from flask_login import current_user
            permiso.revisado_por = current_user.id if hasattr(current_user, 'id') else None
        except ImportError:
            permiso.revisado_por = None

        # Registrar el cambio en el historial usando el servicio de auditoría
        AuditService.registrar_cambio_permiso(
            permiso=permiso,
            tipo_cambio='APROBAR',
            estado_anterior=estado_anterior,
            estado_nuevo='Aprobado',
            observaciones=request.form.get('observaciones', ''),
            usuario=current_user
        )

        # Guardar los cambios
        db.session.commit()

        flash(f"Permiso de {permiso.empleado.nombre} aprobado correctamente", "success")
        logging.info(f"Permiso ID {id} aprobado correctamente")
    except Exception as e:
        db.session.rollback()
        flash(f"Error al aprobar el permiso: {str(e)}", "error")
        logging.error(f"Error al aprobar permiso ID {id}: {str(e)}")
        logging.error(traceback.format_exc())

    return redirect(url_for('permissions.manage_permissions', estado='Aprobado'))

@permissions_bp.route('/denegar/<int:id>', methods=['POST'])
def denegar_permiso(id):
    """Denegar un permiso"""
    try:
        permiso = Permiso.query.get_or_404(id)
        estado_anterior = permiso.estado

        # Actualizar el permiso
        permiso.estado = 'Denegado'
        permiso.observaciones_revision = request.form.get('observaciones', '')
        permiso.fecha_revision = datetime.now()
        # Usar current_user si está disponible
        try:
            from flask_login import current_user
            permiso.revisado_por = current_user.id if hasattr(current_user, 'id') else None
        except ImportError:
            permiso.revisado_por = None

        # Registrar el cambio en el historial usando el servicio de auditoría
        AuditService.registrar_cambio_permiso(
            permiso=permiso,
            tipo_cambio='DENEGAR',
            estado_anterior=estado_anterior,
            estado_nuevo='Denegado',
            observaciones=request.form.get('observaciones', ''),
            usuario=current_user
        )

        # Guardar los cambios
        db.session.commit()

        flash(f"Permiso de {permiso.empleado.nombre} denegado", "warning")
        logging.info(f"Permiso ID {id} denegado correctamente")
    except Exception as e:
        db.session.rollback()
        flash(f"Error al denegar el permiso: {str(e)}", "error")
        logging.error(f"Error al denegar permiso ID {id}: {str(e)}")
        logging.error(traceback.format_exc())

    return redirect(url_for('permissions.manage_permissions', estado='Denegado'))

@permissions_bp.route('/pendiente/<int:id>', methods=['POST'])
def marcar_pendiente_permiso(id):
    """Marcar un permiso como pendiente"""
    try:
        permiso = Permiso.query.get_or_404(id)
        estado_anterior = permiso.estado

        # Actualizar el permiso
        permiso.estado = 'Pendiente'
        permiso.observaciones_revision = request.form.get('observaciones', '')
        permiso.fecha_revision = datetime.now()
        # Usar current_user si está disponible
        try:
            from flask_login import current_user
            permiso.revisado_por = current_user.id if hasattr(current_user, 'id') else None
        except ImportError:
            permiso.revisado_por = None

        # Registrar el cambio en el historial usando el servicio de auditoría
        AuditService.registrar_cambio_permiso(
            permiso=permiso,
            tipo_cambio='PENDIENTE',
            estado_anterior=estado_anterior,
            estado_nuevo='Pendiente',
            observaciones=request.form.get('observaciones', ''),
            usuario=current_user
        )

        # Guardar los cambios
        db.session.commit()

        flash(f"Permiso de {permiso.empleado.nombre} marcado como pendiente", "info")
        logging.info(f"Permiso ID {id} marcado como pendiente correctamente")
    except Exception as e:
        db.session.rollback()
        flash(f"Error al marcar el permiso como pendiente: {str(e)}", "error")
        logging.error(f"Error al marcar permiso ID {id} como pendiente: {str(e)}")
        logging.error(traceback.format_exc())

    return redirect(url_for('permissions.manage_permissions', estado='Pendiente'))

@permissions_bp.route('/eliminar/<int:id>', methods=['POST'])
def eliminar_permiso(id):
    """Eliminar un permiso"""
    try:
        permiso = Permiso.query.get_or_404(id)
        db.session.delete(permiso)
        db.session.commit()
        flash(f"Permiso eliminado correctamente", 'success')
        logging.info(f"Permiso ID {id} eliminado correctamente")
    except Exception as e:
        db.session.rollback()
        flash(f"Error al eliminar el permiso: {str(e)}", "error")
        logging.error(f"Error al eliminar permiso ID {id}: {str(e)}")
        logging.error(traceback.format_exc())

    return redirect(url_for('permissions.manage_permissions'))

# Esta ruta se añade para compatibilidad con las plantillas
@permissions_bp.route('/detalles/<int:id>')
def detalles_permiso(id):
    """Ver detalles de un permiso"""
    permiso = Permiso.query.get_or_404(id)
    return render_template('detalles_permiso.html', permiso=permiso)

@permissions_bp.route('/editar/<int:id>', methods=['GET', 'POST'])
def editar_permiso(id):
    """Editar un permiso existente"""
    permiso = Permiso.query.get_or_404(id)

    if request.method == 'POST':
        try:
            # Obtener datos del formulario
            permiso.empleado_id = request.form.get('empleado_id', type=int)
            permiso.tipo_permiso = request.form.get('tipo_permiso')
            permiso.fecha_inicio = datetime.strptime(request.form.get('fecha_inicio'), '%Y-%m-%d').date()
            permiso.hora_inicio = datetime.strptime(request.form.get('hora_inicio'), '%H:%M').time()
            permiso.fecha_fin = datetime.strptime(request.form.get('fecha_fin'), '%Y-%m-%d').date()
            permiso.hora_fin = datetime.strptime(request.form.get('hora_fin'), '%H:%M').time()
            permiso.motivo = request.form.get('motivo')
            permiso.estado = request.form.get('estado')
            permiso.observaciones_revision = request.form.get('observaciones_revision')
            permiso.es_absentismo = 'es_absentismo' in request.form
            permiso.justificante = request.form.get('justificante')
            permiso.sin_fecha_fin = 'sin_fecha_fin' in request.form

            # Validaciones específicas para bajas médicas sin fecha de fin
            if permiso.tipo_permiso == 'Baja Médica' and permiso.sin_fecha_fin:
                # Para bajas indefinidas, establecer la fecha de fin igual a la de inicio
                # (se usará solo para almacenamiento, el cálculo real usará la fecha actual)
                permiso.fecha_fin = permiso.fecha_inicio
                permiso.hora_fin = permiso.hora_inicio

                # Registrar en el historial si es una conversión a baja indefinida
                if not Permiso.query.get(id).sin_fecha_fin:
                    historial_conversion = HistorialCambios(
                        tipo_cambio='CONVERTIR',
                        entidad='Permiso',
                        entidad_id=permiso.id,
                        descripcion=f"Conversión a baja médica indefinida ID {permiso.id}"
                    )
                    db.session.add(historial_conversion)
            elif permiso.tipo_permiso == 'Baja Médica' and not permiso.sin_fecha_fin:
                # Si se está estableciendo una fecha de fin a una baja que era indefinida
                if Permiso.query.get(id).sin_fecha_fin:
                    historial_finalizacion = HistorialCambios(
                        tipo_cambio='FINALIZAR',
                        entidad='Permiso',
                        entidad_id=permiso.id,
                        descripcion=f"Finalización de baja médica indefinida ID {permiso.id} con fecha {permiso.fecha_fin.strftime('%d/%m/%Y')}"
                    )
                    db.session.add(historial_finalizacion)

                # Validar que la fecha de fin sea posterior o igual a la de inicio
                if permiso.fecha_inicio > permiso.fecha_fin:
                    raise ValueError("La fecha de inicio no puede ser posterior a la fecha de fin")
            else:
                # Para otros tipos de permiso, validar fechas normalmente
                if permiso.fecha_inicio > permiso.fecha_fin:
                    raise ValueError("La fecha de inicio no puede ser posterior a la fecha de fin")

            # Actualizar automáticamente es_absentismo según el tipo de permiso
            permiso.es_absentismo = permiso.tipo_permiso in ['Ausencia', 'Baja Médica']

            # Registrar el cambio en el historial usando el servicio de auditoría
            AuditService.registrar_cambio_permiso(
                permiso=permiso,
                tipo_cambio='EDITAR',
                observaciones=f"Edición de permiso: {permiso.tipo_permiso} del {permiso.fecha_inicio.strftime('%d/%m/%Y')} al {permiso.fecha_fin.strftime('%d/%m/%Y')} - {permiso.motivo}",
                usuario=current_user
            )

            # Guardar los cambios
            db.session.commit()

            flash("Permiso actualizado correctamente", "success")
            return redirect(url_for('permissions.detalles_permiso', id=permiso.id))

        except Exception as e:
            db.session.rollback()
            flash(f"Error al actualizar el permiso: {str(e)}", "error")
            logging.error(f"Error al actualizar permiso ID {id}: {str(e)}")
            logging.error(traceback.format_exc())

    # Para solicitudes GET, mostrar el formulario con los datos actuales
    empleados = Empleado.query.filter_by(activo=True).order_by(Empleado.ficha).all()
    return render_template('permissions/edit.html',
                         permiso=permiso,
                         empleados=empleados,
                         tipos_permiso=TIPOS_PERMISO)


